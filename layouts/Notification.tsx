"use client";

import React, { useState } from "react";
import { AppPopover } from "@/components";
import Link from "next/link";
import {
  ProfileIcon,
  ChevronDownIcon,
  BellIcon,
  AnnouncementIcon,
  GiftIcon,
  EmailIcon20,
} from "@/assets/icons";

export const Notification = () => {
  const [isShow, setIsShow] = useState<boolean>(false);
  return (
    <AppPopover
      position="left"
      trigger={
        <div className="text-white-800 hover:text-white-1000 hidden cursor-pointer p-2 lg:block">
          <BellIcon />
        </div>
      }
      content={
        <div
          style={{
            boxShadow: "4px 4px 8px 0px var(--Black-500, rgba(8, 9, 12, 0.50))",
            background:
              "linear-gradient(0deg, var(--White-100, rgba(255, 255, 255, 0.10)) 0%, var(--White-100, rgba(255, 255, 255, 0.10)) 100%), var(--Black-900, #08090C)",
          }}
          className="z-9999 border-white-100 min-w-[370px] rounded-[12px] border px-4 py-3"
        >
          <div className="border-white-100 flex items-center justify-between border-b pb-2">
            <div className="heading-sm-medium-16">Notification</div>
            <Link href="/notification/announcement">
              <div className="body-md-regular-14 flex items-center gap-4">
                View All <ChevronDownIcon className="rotate-[-90deg]" />
              </div>
            </Link>
          </div>

          <Link href="/notification/announcement">
            <div className="flex justify-between gap-4 py-2">
              <div className="flex items-center gap-2">
                <AnnouncementIcon />
                <div>
                  <div className="body-md-semibold-14">Announcement</div>
                  <div className="body-sm-regular-12 text-white-500 max-w-[222px] truncate">
                    VDAX South Africa Exclusive: Get your...
                  </div>
                </div>
              </div>

              <div className="body-sm-regular-12 text-white-500">04-07</div>
            </div>
          </Link>

          <Link href="/notification/campaign">
            <div className="flex justify-between gap-4 py-2">
              <div className="flex items-center gap-2">
                <GiftIcon />
                <div>
                  <div className="body-md-semibold-14">Campaign</div>
                  <div className="body-sm-regular-12 text-white-500">
                    No news to report
                  </div>
                </div>
              </div>

              {/*<div className="body-sm-regular-12 text-white-500">04-07</div>*/}
            </div>
          </Link>

          <Link href="/notification/marketing-transaction">
            <div className="flex justify-between gap-4 py-2">
              <div className="flex items-center gap-2">
                <EmailIcon20 />
                <div>
                  <div className="body-md-semibold-14">
                    Marketing & Transactional
                  </div>
                  <div className="body-sm-regular-12 text-white-500">
                    No news to report
                  </div>
                </div>
              </div>

              {/*<div className="body-sm-regular-12 text-white-500">04-07</div>*/}
            </div>
          </Link>

          <Link href="/notification/account-assistant">
            <div className="flex justify-between gap-4 py-2">
              <div className="flex items-center gap-2">
                <ProfileIcon />
                <div>
                  <div className="body-md-semibold-14">Account Assistant</div>
                  <div className="body-sm-regular-12 text-white-500">
                    Login attempted from new IP
                  </div>
                </div>
              </div>

              <div className="body-sm-regular-12 text-white-500">04-07</div>
            </div>
          </Link>
        </div>
      }
      isOpen={isShow}
      onToggle={() => setIsShow(!isShow)}
      onClose={() => setIsShow(false)}
    />
  );
};

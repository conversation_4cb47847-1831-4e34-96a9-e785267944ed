import config from "@/config";
import BaseRootRequest from "./BaseRequest";

export default class AccountRequest extends BaseRootRequest {
  getUrlPrefix() {
    return config.apiUrl;
  }

  deposit(params: { networkId: number; asset: string }) {
    const url = `/v1/deposit-address`;
    return this.post(url, params);
  }

  withdraw(params: {
    amount: number;
    asset: string;
    toAddress?: string | null;
    networkId: number;
    userId?: string | number | null;
  }) {
    const url = `/v1/withdraw`;
    return this.post(url, params);
  }

  getDepositTransactions(params: any) {
    const url = `/v1/deposit-transactions`;
    return this.get(url, params);
  }

  getWithdrawTransactions(params: any) {
    const url = `/v1/withdraw-transactions`;
    return this.get(url, params);
  }

  getTransactionHistories(params: any) {
    const url = `/v1/transaction-histories`;
    return this.get(url, params);
  }

  getAccount = (omitZeroBalances = true) => {
    const url = `/v1/account`;
    return this.get(url, { omitZeroBalances });
  };
}

export type TCandle = [
  number, // Kline open time
  string, // Open price
  string, // High price
  string, // Low price
  string, // Close price
  string, // Volume
  number, // Kline Close time
  string // Quote asset volume
];
export type TUpdatedCandleWsData = {
  e: string; // Event type
  E: number; // Event time
  s: string; // Symbol
  k: {
    t: number; // Kline start time
    T: number; // Kline close time
    s: string; // Symbol
    i: string; // Interval
    f: number; // First trade ID
    L: number; // Last trade ID
    o: string; // Open price
    c: string; // Close price
    h: string; // High price
    l: string; // Low price
    v: string; // Base asset volume
    n: number; // Number of trades
    x: boolean; // Is this kline closed?
    q: string; // Quote asset volume
    V: string; // Taker buy base asset volume
    Q: string; // Taker buy quote asset volume
    B: string; // Ignore
  };
};

export type TradingPair = {
  symbol: string;
  lastPrice: string | null;
  highPrice: string | null;
  lowPrice: string | null;
  priceChangePercent: string | null;
  priceChange: string | null;
  baseVolume: string | null;
  quoteVolume: string | null;
  timestamp: number | null;
  isUp: boolean; // up or down compare to last price
  isUp24h: boolean; // up or down compare to 24h price
};

export type PairSetting = {
  createdAt: number;
  feeMaker: string;
  feeTaker: string;
  marketId: number;
  maxMarketOrderAmount: string;
  maxOpenLimitOrders: number;
  maxOrderSize: string;
  maxPrice: string;
  maxPriceMultiplier: string;
  maxQuantity: string;
  maxTradeAmount: string;
  minAmountMovement: string;
  minOrderSize: string;
  minPrice: string;
  minPriceMovement: string;
  minPriceMultiplier: string;
  minQuantity: string;
  minQuoteQty: string;
  minTradeAmount: string;
  pricePrecision: number;
  quantityPrecision: number;
  symbol: string; // BTCUSDT
  quoteAsset: string; // USDT
  baseAsset: string; // BTC
  status: string;
  updatedAt: number;
};

export enum ETradingStatus {
  ACTIVE = "ACTIVE",
  INACTIVE = "INACTIVE",
}

export type TPairSetting = {
  id?: string; // Unique identifier for the trading pair
  marketId?: string; // Market ID (can be empty or external reference)
  symbol: string; // Symbol representing the trading pair (e.g., BTCUSDT)
  baseAsset: string; // The base asset being traded (e.g., BTC)
  quoteAsset: string; // The quote asset used to price the base asset (e.g., USDT)
  pricePrecision: number; // Number of decimal places allowed for the price
  minQuantity: string; // Minimum quantity of base asset allowed per order
  maxQuantity: string; // Maximum quantity of base asset allowed per order
  quantityPrecision: number; // Number of decimal places allowed for quantity
  minQuoteQty: string; // Minimum total value (price * quantity) of the order
  feeMaker: string; // Maker fee rate (for liquidity providers)
  feeTaker: string; // Taker fee rate (for liquidity takers)
  status: ETradingStatus;
};

export type TPairMarket = {
  name: string | any;
  status?: string;
  type?: string;
  value: string;
};

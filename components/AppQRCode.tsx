"use client";

import QRCodeStyling, {
  CornerSquareType,
  CornerDotType,
} from "qr-code-styling";
import React from "react";

export const AppQRCode = ({
  value,
  className = "md:w-[72px] md:h-[72px] w-[64px] h-[64px]",
  color = "#ffffff",
}: {
  value: string;
  className?: string;
  color?: any;
}) => {
  const [qrImage, setQrImage] = React.useState<string>("");

  React.useEffect(() => {
    const generateQR = async () => {
      const qrCode = new QRCodeStyling({
        width: 160,
        height: 160,
        type: "svg",
        image: "/images/LogoQR.png",
        shape: "square",
        data: value,
        dotsOptions: {
          color: color,
          type: "rounded",
          roundSize: true,
        },
        backgroundOptions: {
          color: "transparent",
        },
        margin: 0,
        imageOptions: {
          hideBackgroundDots: true,
          crossOrigin: "anonymous",
          margin: 0,
          imageSize: 0.5,
          saveAsBlob: true,
        },
        cornersSquareOptions: {
          color: "#222222",
          type: "extra-rounded" as CornerSquareType,
          gradient: {
            type: "linear",
            rotation: 180,
            colorStops: [
              { offset: 0, color: color },
              { offset: 1, color: color },
            ],
          },
        },
        cornersDotOptions: {
          color: "#222222",
          type: "dot" as CornerDotType,
          gradient: {
            type: "linear",
            rotation: 180,
            colorStops: [
              { offset: 0, color: color },
              { offset: 1, color: color },
            ],
          },
        },
      });

      try {
        const blob = await qrCode.getRawData("svg");
        if (blob) {
          const url = URL.createObjectURL(blob as Blob);
          setQrImage(url);
        }
      } catch (error) {
        console.error("Error generating QR code:", error);
      }
    };

    generateQR().then();
  }, [value]);

  if (!qrImage) return <></>;

  return <img src={qrImage} alt="QR Code" className={className} />;
};

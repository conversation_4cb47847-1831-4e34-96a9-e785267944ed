"use client";

import { usePairContext } from "@/app/trade/[symbol]/provider";
import { StarActiveIcon, StarIcon } from "@/assets/icons";
import { formatPrice } from "@/utils/format";
import { getPriceStyle } from "@/utils/helper";
import { memo, useEffect, useState } from "react";
import { useSelector } from "react-redux";
import {
  getTickerRoom,
  subscribeSocketChannel,
  unsubscribeSocketChannel,
} from "@/libs/socket";
import { RootState } from "@/store";
import { PairSymbol } from "./PairSymbol";
import { AppBroadcast, BROADCAST_EVENTS } from "@/libs/broadcast";
import AppNumber from "./AppNumber";

export const PairTicker = memo(() => {
  const [isFavorite, setIsFavorite] = useState<boolean>(true);
  const { symbol, pairSetting } = usePairContext();
  const { tradingPair } = useSelector((state: RootState) => state.tradingPair);

  const socketConnected = useSelector(
    (state: RootState) => state.metadata.socketConnected
  );

  // We are handling ticker update in app/trade/[symbol]/provider
  useEffect(() => {
    if (!symbol || !socketConnected) {
      return;
    }

    subscribeSocketChannel({
      params: [getTickerRoom(symbol)],
    });

    return () => {
      unsubscribeSocketChannel({
        params: [getTickerRoom(symbol)],
      });
    };
  }, [symbol, socketConnected]);

  useEffect(() => {
    const loadFavorites = () => {
      try {
        const storedFavorites = localStorage.getItem("favoritePairs");
        if (storedFavorites) {
          setIsFavorite(storedFavorites?.includes(symbol?.toUpperCase()));
        }
      } catch (error) {
        console.log("Error loading favorites:", error);
      }
    };

    loadFavorites();
  }, []);

  const handleToggleFavorite = () => {
    setIsFavorite(!isFavorite);
    AppBroadcast.dispatch(BROADCAST_EVENTS.TOGGLE_FAVORITE, { symbol });
  };

  return (
    <div className="border-white-100 bg-black-900 flex h-auto justify-between gap-8 border-b px-4 py-2 lg:h-[60px] lg:items-center lg:justify-start lg:bg-transparent">
      <div className="flex flex-col gap-1 lg:flex-row lg:gap-8">
        <div className="flex items-center gap-3">
          <div
            onClick={handleToggleFavorite}
            className="border-white-100 hidden cursor-pointer rounded-[4px] border p-1.5 lg:block"
          >
            {isFavorite ? <StarActiveIcon /> : <StarIcon />}
          </div>
          <PairSymbol symbol={symbol} />
        </div>

        <div className="flex flex-col gap-1">
          <div
            className="heading-lg-medium-24"
            style={{
              color: getPriceStyle(tradingPair.isUp),
            }}
          >
            {formatPrice(
              tradingPair.lastPrice || 0,
              pairSetting?.pricePrecision
            )}
          </div>
        </div>
      </div>

      <div className="flex gap-4 lg:gap-6">
        <div className="hidden flex-col gap-1.5 lg:flex">
          <div className="body-sm-regular-12 text-white-500">24h change</div>
          <div
            className="body-sm-medium-12 flex items-center gap-1"
            style={{ color: getPriceStyle(tradingPair.isUp24h) }}
          >
            <div className="flex">
              $
              <AppNumber
                value={tradingPair?.priceChange || 0}
                decimals={pairSetting?.pricePrecision}
                isFormatLargeNumber={false}
              />
            </div>
            <div className="flex">
              <AppNumber
                value={tradingPair?.priceChangePercent || 0}
                decimals={2}
                isFormatLargeNumber={false}
              />
              %
            </div>
          </div>
        </div>

        <div className="flex flex-col gap-3 lg:flex-row lg:gap-6">
          <div className="flex flex-col gap-1.5">
            <div className="body-sm-regular-12 text-white-500">24h High</div>
            <div className="body-sm-medium-12 text-white-900">
              {formatPrice(
                tradingPair.highPrice || 0,
                pairSetting?.pricePrecision
              )}
            </div>
          </div>

          <div className="flex flex-col gap-1.5">
            <div className="body-sm-regular-12 text-white-500">24h Low</div>
            <div className="body-sm-medium-12 text-white-900">
              {formatPrice(
                tradingPair.lowPrice || 0,
                pairSetting?.pricePrecision
              )}
            </div>
          </div>
        </div>

        <div className="flex flex-col gap-3 lg:flex-row lg:gap-6 ">
          <div className="flex flex-col gap-1.5">
            <div className="body-sm-regular-12 text-white-500">
              24h Volume ({pairSetting?.baseAsset?.toUpperCase()})
            </div>
            <div className="body-sm-medium-12 text-white-900">
              {formatPrice(
                tradingPair?.baseVolume || 0,
                pairSetting?.quantityPrecision
              )}
            </div>
          </div>

          <div className="flex flex-col gap-1.5">
            <div className="body-sm-regular-12 text-white-500">
              24h Volume ({pairSetting?.quoteAsset?.toUpperCase()})
            </div>
            <div className="body-sm-medium-12 text-white-900">
              {formatPrice(tradingPair?.quoteVolume || 0)}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
});

PairTicker.displayName = "PairTicker";

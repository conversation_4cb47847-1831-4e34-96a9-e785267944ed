"use client";

import React, { useRef, useEffect } from "react";
import { errorMsg } from "@/libs/toast";

export const AppInputCodeVerify = ({
  values,
  setValues,
  length,
}: {
  length: number;
  values: string[];
  setValues: (value: string[]) => void;
}) => {
  const CODE_LENGTH = length;

  const inputs = useRef<any>([]);

  useEffect(() => {
    inputs.current = inputs.current.slice(0, CODE_LENGTH);
  }, []);

  const handleChange = (val: any, index: number) => {
    const newChar = val.replace(/\D/g, ""); // ❗ chỉ lấy số
    if (newChar) {
      const newValues = [...values];
      newValues[index] = newChar;
      setValues(newValues);
      if (index < CODE_LENGTH - 1) {
        inputs.current[index + 1]?.focus();
      }
    }
  };

  const handleKeyDown = (e: any, index: number) => {
    if (e.key === "Backspace") {
      e.preventDefault();
      const newValues = [...values];

      if (values[index]) {
        newValues[index] = "";
        setValues(newValues);
      } else if (index > 0) {
        inputs.current[index - 1]?.focus();
        newValues[index - 1] = "";
        setValues(newValues);
      }
    }
  };

  const applyPasteValue = (text: string) => {
    const clean = text.replace(/\D/g, "").slice(0, CODE_LENGTH);
    const chars = clean.split("");
    const newValues = [...values];

    chars.forEach((char, i) => {
      newValues[i] = char;
    });

    setValues(newValues);
    const nextIndex = Math.min(chars.length, CODE_LENGTH - 1);
    inputs.current[nextIndex]?.focus();
  };

  const handlePaste = (e: any) => {
    e.preventDefault();
    const paste = e.clipboardData.getData("text");
    applyPasteValue(paste);
  };

  const handlePasteButtonClick = async () => {
    try {
      const text = await navigator?.clipboard?.readText();
      applyPasteValue(text);
    } catch (err: any) {
      errorMsg("Something went wrong!");
    }
  };

  return (
    <div>
      <div className="flex gap-2">
        {values.map((val, i) => (
          <input
            key={i}
            ref={(el) => {
              inputs.current[i] = el;
            }}
            type="tel"
            inputMode="numeric"
            maxLength={1}
            autoFocus={i === 0}
            value={val}
            onChange={(e) => handleChange(e.target.value, i)}
            onKeyDown={(e) => handleKeyDown(e, i)}
            onPaste={handlePaste}
            className="border-white-100 h-[80px] w-[60px] rounded-lg border text-center text-[20px] focus:border-green-500 focus:outline-none"
          />
        ))}
      </div>
      <div
        onClick={handlePasteButtonClick}
        className="body-sm-medium-12 mr-[10px] mt-2 flex cursor-pointer justify-end text-green-500"
      >
        <div>Paste</div>
      </div>
    </div>
  );
};

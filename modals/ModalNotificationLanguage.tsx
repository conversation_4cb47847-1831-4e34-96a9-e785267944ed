import React, { useState } from "react";
import { BaseModal } from "@/modals/BaseModal";
import {
  CloseIcon24,
  RadioCheckedIcon,
  RadioNoCheckIcon,
} from "@/assets/icons";
import { AppButton } from "@/components";
import { useMediaQuery } from "react-responsive";

const OPTIONS_LANGUAGE = [
  {
    name: "English",
    value: "English",
  },
  {
    name: "Русский",
    value: "Русский",
  },
  {
    name: "简体中文",
    value: "简体中文",
  },
  {
    name: "Español(Internacional)",
    value: "Español(Internacional)",
  },
  {
    name: "Türkçe",
    value: "Türkçe",
  },
  {
    name: "Français",
    value: "Français",
  },
  {
    name: "Português",
    value: "Português",
  },
  {
    name: "日本語",
    value: "日本語",
  },
  {
    name: "<PERSON><PERSON><PERSON>",
    value: "Deutsch",
  },
];

export const ModalNotificationLanguage = ({
  isOpen,
  onClose,
}: {
  isOpen: boolean;
  onClose: () => void;
}) => {
  const [language, setLanguage] = useState<string>("English");
  const isMobile = useMediaQuery({ query: "(max-width: 768px)" });

  return (
    <BaseModal
      className="w-screen md:w-[500px]"
      isOpen={isOpen}
      onClose={onClose}
      isBottom={isMobile}
    >
      <div className=" flex justify-between">
        <div className="heading-md-semibold-18">Notification Language</div>
        <div onClick={onClose} className="cursor-pointer">
          <CloseIcon24 />
        </div>
      </div>

      <div className="mt-7 flex flex-col gap-4">
        {OPTIONS_LANGUAGE.map((item, index) => {
          return (
            <div
              key={index}
              className="flex cursor-pointer items-center gap-2"
              onClick={() => setLanguage(item.value)}
            >
              <div>
                {language === item.value ? (
                  <RadioCheckedIcon />
                ) : (
                  <RadioNoCheckIcon />
                )}
              </div>
              <div className="body-md-regular-14">{item.name}</div>
            </div>
          );
        })}
      </div>

      <div className="mt-8 grid grid-cols-2 gap-3">
        <AppButton variant="secondary" size="large" onClick={onClose}>
          Cancel
        </AppButton>
        <AppButton variant="buy" size="large">
          Confirm
        </AppButton>
      </div>
    </BaseModal>
  );
};

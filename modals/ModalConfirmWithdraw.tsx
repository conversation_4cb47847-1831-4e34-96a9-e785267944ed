import { BaseModal } from "@/modals/BaseModal";
import React from "react";
import { CloseIcon } from "@/assets/icons";
import { AppButton } from "@/components";
import { TNetwork } from "@/types/network";
import {
  EType,
  ETypeUser,
} from "../app/(deposit-withdraw)/my/withdraw/_parts/WithdrawForm";
import { useMediaQuery } from "react-responsive";

export const ModalConfirmWithdraw = ({
  isLoading,
  isOpen,
  onClose,
  token,
  amount,
  network,
  address,
  onWithdraw,
  type,
  email,
  userId,
  note,
  typeUser,
}: {
  email: string;
  typeUser: string;
  userId: string;
  note: string;
  type: string;
  token: string;
  address: string;
  network: TNetwork;
  amount: string;
  isOpen: boolean;
  isLoading: boolean;
  onClose: () => void;
  onWithdraw: () => void;
}) => {
  const isMobile = useMediaQuery({ query: "(max-width: 768px)" });

  const _renderForTypeAddress = () => {
    return (
      <>
        <div className="body-md-regular-14 grid grid-cols-2 py-2">
          <div className="text-white-500">Address</div>
          <div className="text-right">{address}</div>
        </div>
        <div className="body-md-regular-14 grid grid-cols-2 py-2">
          <div className="text-white-500">Network</div>
          <div className="text-right">{network?.name}</div>
        </div>
        <div className="body-md-regular-14 grid grid-cols-2 py-2">
          <div className="text-white-500">Amount</div>
          <div className="text-right">
            {amount} {token}
          </div>
        </div>
        <div className="body-md-regular-14 grid grid-cols-2 py-2">
          <div className="text-white-500">Receive</div>
          <div className="text-right">-- {token}</div>
        </div>
        <div className="body-md-regular-14 border-white-100 grid grid-cols-2 border-b py-2">
          <div className="text-white-500">Fee</div>
          <div className="text-right">-- {token}</div>
        </div>
        <ul className="body-sm-regular-12 text-white-500 mb-6 list-disc pl-6 pt-1">
          <li> Make sure address was matched on a chain</li>
          <li> Non-cancelable transaction</li>
        </ul>
      </>
    );
  };

  const _renderForTypeVDAXUser = () => {
    return (
      <>
        <div className="body-md-regular-14 grid grid-cols-2 py-2">
          <div className="text-white-500">Send mode</div>
          <div className="text-right">
            {typeUser === ETypeUser.EMAIL ? "Email" : "VDAX ID"}
          </div>
        </div>
        <div className="body-md-regular-14 grid grid-cols-2 py-2">
          <div className="text-white-500">Send to</div>
          <div className="text-right">
            {typeUser === ETypeUser.EMAIL ? email : userId}
          </div>
        </div>
        <div className="body-md-regular-14 grid grid-cols-2 py-2">
          <div className="text-white-500">You Pay</div>
          <div className="text-right">
            {amount} {token}
          </div>
        </div>
        <div className="body-md-regular-14 grid grid-cols-2 py-2">
          <div className="text-white-500">Payee Receive</div>
          <div className="text-right">-- {token}</div>
        </div>
        <div className="body-md-regular-14 grid grid-cols-2 py-2">
          <div className="text-white-500">Send from</div>
          <div className="text-right">Spot Wallet</div>
        </div>
        <div className="body-md-regular-14 border-white-100 grid grid-cols-2 border-b py-2">
          <div className="text-white-500">Note</div>
          <div className="text-right">{note || "--"}</div>
        </div>
        <ul className="body-sm-regular-12 text-white-500 mb-6 list-disc pl-6 pt-1">
          <li>Ensure that the receipient’s information is correct</li>
          <li>Funds will be sent immediately and are not refundable</li>
        </ul>
      </>
    );
  };

  return (
    <BaseModal
      className="w-screen max-w-[500px]"
      isOpen={isOpen}
      onClose={onClose}
      isBottom={isMobile}
    >
      <div className="flex items-center justify-between py-1">
        <div className="heading-sm-medium-16">Confirm Withdrawal</div>
        <CloseIcon className="cursor-pointer" onClick={onClose} />
      </div>

      {type === EType.ADDRESS
        ? _renderForTypeAddress()
        : _renderForTypeVDAXUser()}

      <AppButton
        size="large"
        variant="buy"
        onClick={onWithdraw}
        disabled={isLoading}
      >
        Continue
      </AppButton>
    </BaseModal>
  );
};

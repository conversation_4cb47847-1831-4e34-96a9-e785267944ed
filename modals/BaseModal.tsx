"use client";

import cx from "classnames";
import React, { ReactNode, useEffect } from "react";
import ReactModal from "react-modal";

interface Props {
  children: ReactNode;
  isOpen: boolean;
  onClose?: VoidFunction;
  title?: string;
  description?: string;
  zIndex?: number;
  className?: string;
  headerClassName?: string;
  titleClassName?: string;
  descClassName?: string;
  width?: string;
  isBottom?: boolean;
}

export const BaseModal = ({
  isOpen,
  children,
  onClose,
  title,
  description,
  className,
  headerClassName,
  titleClassName,
  descClassName,
  zIndex,
  isBottom,
}: Props) => {
  const customStyles = isBottom
    ? {
        content: {
          top: "auto",
          bottom: "0",
          left: "0",
          right: "0",
          borderRadius: "16px 16px 0 0",
          padding: 0,
          background: "#212225",
          overflow: "inherit",
          border: 0,
          boxShadow: "4px 4px 8px 0px rgba(8, 9, 12, 0.50)",
        },
        overlay: {
          background: "rgba(8, 9, 12, 0.60)",
          zIndex: 999,
        },
      }
    : {
        content: {
          top: "50%",
          left: "50%",
          right: "auto",
          bottom: "auto",
          marginRight: "-50%",
          transform: "translate(-50%, -50%)",
          borderRadius: "16px",
          padding: 0,
          background: "#212225",
          overflow: "inherit",
          border: "1px solid rgba(255, 255, 255, 0.1)",
          boxShadow: "4px 4px 8px 0px rgba(8, 9, 12, 0.50)",
        },
        overlay: {
          background: "rgba(8, 9, 12, 0.60)",
          zIndex: zIndex ?? 999,
        },
      };

  useEffect(() => {
    document.body.style.overflow = isOpen ? "hidden" : "auto";
    return () => {
      document.body.style.overflow = "auto";
    };
  }, [isOpen]);

  return (
    <ReactModal
      isOpen={isOpen}
      onRequestClose={onClose}
      style={customStyles}
      ariaHideApp={false}
      bodyOpenClassName="overflow-hidden"
    >
      <div
        className={`relative min-w-[300px] p-[16px] md:min-w-[420px] ${className}`}
      >
        {(!!title || !!description) && (
          <div
            className={cx(
              "mb-[16px] flex flex-col items-center",
              headerClassName
            )}
          >
            {title && (
              <div className={cx("body-md-semibold-14", titleClassName)}>
                {title}
              </div>
            )}

            {description && (
              <div
                className={`heading-sm-medium-16 text-center !font-normal ${descClassName}`}
              >
                {description}
              </div>
            )}
          </div>
        )}

        <div className={"w-full"}>{children}</div>
      </div>
    </ReactModal>
  );
};

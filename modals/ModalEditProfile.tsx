import React, { useEffect, useState, useRef } from "react";
import { BaseModal } from "@/modals/BaseModal";
import { CloseIcon24, CameraIcon } from "@/assets/icons";
import { useSelector } from "react-redux";
import { RootState } from "../store/index";
import { AppButton } from "@/components";
import Image from "next/image";
import { useMediaQuery } from "react-responsive";

export const ModalEditProfile = ({
  isOpen,
  onClose,
}: {
  isOpen: boolean;
  onClose: () => void;
}) => {
  const [name, setName] = useState<string>("");
  const userInfo = useSelector((state: RootState) => state.user.userInfo);
  const fileInputRef = useRef<any>(null);
  const [preview, setPreview] = useState<string | null>(null);
  const isMobile = useMediaQuery({ query: "(max-width: 768px)" });

  const handleClick = () => {
    fileInputRef?.current?.click();
  };

  const handleFileChange = (e: any) => {
    const file = e.target.files[0];
    if (file && file.type.startsWith("image/")) {
      setPreview(URL.createObjectURL(file));
    }
  };

  useEffect(() => {
    if (!userInfo) return;
    setName(userInfo?.name || "");
  }, [userInfo]);

  return (
    <BaseModal
      className="w-screen md:w-[500px]"
      isOpen={isOpen}
      onClose={onClose}
      isBottom={isMobile}
    >
      <div className=" flex justify-between">
        <div className="heading-md-semibold-18">Edit Profile</div>
        <div onClick={onClose} className="cursor-pointer">
          <CloseIcon24 />
        </div>
      </div>

      <div className="border-white-100 mt-7 flex flex-col items-center gap-2 border-b pb-4">
        <div className="relative">
          <div className="bg-white-50 h-[64px] w-[64px] rounded-full">
            {preview ? (
              <img
                src={preview}
                alt="avatar"
                className="aspect-square h-[64px] !w-[64px] rounded-full object-cover"
              />
            ) : (
              <>
                {userInfo.avatar ? (
                  <img
                    src={userInfo.avatar}
                    alt="avatar"
                    className="aspect-square h-[64px] !w-[64px] rounded-full object-cover"
                  />
                ) : (
                  <Image
                    src={"/images/AvatarDefault.png"}
                    alt="avatar"
                    width={64}
                    height={64}
                    className="aspect-square h-[64px] !w-[64px] rounded-full object-cover"
                    unoptimized
                  />
                )}
              </>
            )}
          </div>
          <input
            type="file"
            accept="image/*"
            ref={fileInputRef}
            onChange={handleFileChange}
            className="hidden"
          />
          <div
            onClick={handleClick}
            className="bg-black-900/80 absolute bottom-0 right-0 cursor-pointer rounded-full p-1.5"
          >
            <CameraIcon />
          </div>
        </div>

        <div className="body-sm-regular-12 text-white-500">
          Avatar can only be modified 1 time per 30 days.
        </div>
      </div>

      <div className="mt-4">
        <div className="body-sm-medium-12 text-white-700 mb-2">Nickname</div>

        <div className="border-white-100 flex justify-between gap-2 rounded-[6px] border px-3 py-2">
          <input
            value={name}
            onChange={(e) => {
              if (e.target.value.length > 60) return;
              setName(e.target.value);
            }}
            className="placeholder:text-white-300 body-sm-medium-12 flex-1 border-0 bg-transparent outline-none"
            placeholder="Enter Nickname"
          />
          <div className="body-sm-regular-12">{name.length}/60</div>
        </div>

        <div className="body-xs-regular-10 text-white-500 mt-1">
          Nickname can only be modified 1 time per 30 days.
        </div>
      </div>

      <div className="mt-8 grid grid-cols-2 gap-3">
        <AppButton variant="secondary" size="large" onClick={onClose}>
          Cancel
        </AppButton>
        <AppButton variant="buy" size="large">
          Confirm
        </AppButton>
      </div>
    </BaseModal>
  );
};

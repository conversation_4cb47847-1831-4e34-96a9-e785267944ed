import React, { useState } from "react";
import { BaseModal } from "@/modals/BaseModal";
import {
  CloseIcon24,
  RadioCheckedIcon,
  RadioNoCheckIcon,
} from "@/assets/icons";
import { AppButton } from "@/components";
import { useMediaQuery } from "react-responsive";

const OPTIONS_LANGUAGE = [
  {
    name: "Last 24 hours",
    value: "last24hours",
  },
  {
    name: "UTC +7, 00:00 (Current)",
    value: "+7",
  },
  {
    name: "UTC +12, 00:00",
    value: "+12",
  },
  {
    name: "UTC +11, 00:00",
    value: "+11",
  },
  {
    name: "UTC +10, 00:00",
    value: "+10",
  },
  {
    name: "UTC +9, 00:00",
    value: "+9",
  },
  {
    name: "UTC +8, 00:00",
    value: "+8",
  },
  {
    name: "UTC +7, 00:00",
    value: "+7",
  },
  {
    name: "UTC +6, 00:00",
    value: "+6",
  },
];

export const ModalSettingTimezone = ({
  isOpen,
  onClose,
}: {
  isOpen: boolean;
  onClose: () => void;
}) => {
  const [timezone, setTimezone] = useState<string>("last24hours");
  const isMobile = useMediaQuery({ query: "(max-width: 768px)" });

  return (
    <BaseModal
      className="w-screen md:w-[500px]"
      isOpen={isOpen}
      onClose={onClose}
      isBottom={isMobile}
    >
      <div className=" flex justify-between">
        <div className="heading-md-semibold-18">UTC Time Zone</div>
        <div onClick={onClose} className="cursor-pointer">
          <CloseIcon24 />
        </div>
      </div>

      <div className="mt-7 flex flex-col gap-4">
        {OPTIONS_LANGUAGE.map((item, index) => {
          return (
            <div
              key={index}
              className="flex cursor-pointer items-center gap-2"
              onClick={() => setTimezone(item.value)}
            >
              <div>
                {timezone === item.value ? (
                  <RadioCheckedIcon />
                ) : (
                  <RadioNoCheckIcon />
                )}
              </div>
              <div className="body-md-regular-14">{item.name}</div>
            </div>
          );
        })}
      </div>

      <div className="mt-8 grid grid-cols-2 gap-3">
        <AppButton variant="secondary" size="large" onClick={onClose}>
          Cancel
        </AppButton>
        <AppButton variant="buy" size="large">
          Confirm
        </AppButton>
      </div>
    </BaseModal>
  );
};

import React, { useState } from "react";
import { BaseModal } from "@/modals/BaseModal";
import { CloseIcon24, CheckboxCheckedIcon, CheckboxIcon } from "@/assets/icons";
import { AppButton } from "@/components";
import { useMediaQuery } from "react-responsive";

const OPTIONS_NOTIFICATION = [
  {
    name: "Activities",
    value: "activities",
  },
  {
    name: "Trade Notification",
    value: "trade-notification",
  },
  {
    name: "VDAX News",
    value: "news",
  },
  {
    name: "System Messages",
    value: "system-messages",
  },
];

export const ModalNotificationPreference = ({
  isOpen,
  onClose,
}: {
  isOpen: boolean;
  onClose: () => void;
}) => {
  const [type, setType] = useState<string[]>([]);
  const typeAll = OPTIONS_NOTIFICATION.map((item) => item.value);
  const isMobile = useMediaQuery({ query: "(max-width: 768px)" });

  const onSelect = (typeSelected: string) => {
    if (type?.includes(typeSelected)) {
      const newTypes = type.filter((item) => item !== typeSelected);
      setType(newTypes);
      return;
    }

    setType([...type, typeSelected]);
  };

  const onSelectAll = () => {
    if (typeAll.every((item) => type?.includes(item))) {
      setType([]);
      return;
    }

    setType(typeAll);
  };

  return (
    <BaseModal
      className="w-screen md:w-[500px]"
      isOpen={isOpen}
      onClose={onClose}
      isBottom={isMobile}
    >
      <div className=" flex justify-between">
        <div className="heading-md-semibold-18">Notification Preference</div>
        <div onClick={onClose} className="cursor-pointer">
          <CloseIcon24 />
        </div>
      </div>

      <div className="mt-7 flex flex-col gap-4">
        {OPTIONS_NOTIFICATION.map((item, index) => {
          return (
            <div
              key={index}
              className="flex cursor-pointer items-center gap-2"
              onClick={() => onSelect(item.value)}
            >
              <div>
                {type?.includes(item.value) ? (
                  <CheckboxCheckedIcon />
                ) : (
                  <CheckboxIcon />
                )}
              </div>
              <div className="body-md-regular-14">{item.name}</div>
            </div>
          );
        })}

        <div
          className="flex cursor-pointer items-center gap-2"
          onClick={onSelectAll}
        >
          <div>
            {typeAll.every((item) => type?.includes(item)) ? (
              <CheckboxCheckedIcon />
            ) : (
              <CheckboxIcon />
            )}
          </div>
          <div className="body-md-regular-14">Select All</div>
        </div>
      </div>

      <div className="mt-8 grid grid-cols-2 gap-3">
        <AppButton variant="secondary" size="large" onClick={onClose}>
          Cancel
        </AppButton>
        <AppButton variant="buy" size="large">
          Confirm
        </AppButton>
      </div>
    </BaseModal>
  );
};

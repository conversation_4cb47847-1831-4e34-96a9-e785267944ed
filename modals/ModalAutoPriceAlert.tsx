import React from "react";
import { BaseModal } from "@/modals/BaseModal";
import { CloseIcon24 } from "@/assets/icons";
import { AppButton, AppToggle } from "@/components";
import { useMediaQuery } from "react-responsive";

export const ModalAutoPriceAlert = ({
  isOpen,
  onClose,
  isNotification,
  isNotificationSound,
  setIsNotificationSound,
  setIsNotification,
}: {
  isOpen: boolean;
  isNotification: boolean;
  isNotificationSound: boolean;
  setIsNotificationSound: (value: boolean) => void;
  setIsNotification: (value: boolean) => void;
  onClose: () => void;
}) => {
  const isMobile = useMediaQuery({ query: "(max-width: 768px)" });

  return (
    <BaseModal
      className="w-screen md:w-[500px]"
      isOpen={isOpen}
      onClose={onClose}
      isBottom={isMobile}
    >
      <div className=" flex justify-between">
        <div className="heading-md-semibold-18">Auto Price Alert</div>
        <div onClick={onClose} className="cursor-pointer">
          <CloseIcon24 />
        </div>
      </div>

      <div className="mt-7 flex items-center justify-between gap-4">
        <div className="flex-1">
          <div className="heading-sm-medium-16 mb-1">Notification</div>
          <div className="body-md-regular-14 text-white-500">
            Once open auto price alert, you will receive notifications on price
            changes for major and holding cryptos on website.
          </div>
        </div>
        <AppToggle
          value={isNotification}
          onChange={() => setIsNotification(!isNotification)}
        />
      </div>

      <div className="mt-6 flex items-center justify-between gap-4">
        <div className="flex-1">
          <div className="heading-sm-medium-16 mb-1">Notification sound</div>
          <div className="body-md-regular-14 text-white-500">
            Notification sound synchronized with price alerts
          </div>
        </div>
        <AppToggle
          value={isNotificationSound}
          onChange={() => setIsNotificationSound(!isNotificationSound)}
        />
      </div>

      <div className="mt-8 grid grid-cols-2 gap-3">
        <AppButton variant="secondary" size="large" onClick={onClose}>
          Cancel
        </AppButton>
        <AppButton variant="buy" size="large">
          Confirm
        </AppButton>
      </div>
    </BaseModal>
  );
};

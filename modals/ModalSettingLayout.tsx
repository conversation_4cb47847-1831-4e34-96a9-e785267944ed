"use client";

import { App<PERSON><PERSON><PERSON>, <PERSON>ppToggle, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/components";
import {
  CloseIcon,
  ChevronDownIcon,
  SettingColorDownUpIcon,
} from "@/assets/icons";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/store/index";
import { setIsLayoutAdvanced } from "@/store/metadata.store";
import { useState } from "react";

export const ModalSettingLayout = ({
  isOpen,
  onClose,
}: {
  isOpen: boolean;
  onClose: () => void;
}) => {
  const [isHasFavourite, setIsHasFavourite] = useState(false);
  const [isHasChart, setIsHasChart] = useState(true);
  const [isHasOrderBook, setIsHasOrderBook] = useState(true);
  const [isHasTrades, setIsHasTrades] = useState(true);
  const [isHasOpenOrders, setIsHasOpenOrders] = useState(true);
  const [isHasPlaceOrder, setIsHasPlaceOrder] = useState(true);

  const dispatch = useDispatch();
  const isLayoutAdvanced = useSelector(
    (state: RootState) => state.metadata.isLayoutAdvanced
  );

  return (
    <AppDrawer
      isOpen={isOpen}
      className="border-white-100 !lg:w-[420px] !lg:h-screen !bottom-0 !top-auto !z-[9999] !w-[375px] border-l !bg-[#212225]"
      enableOverlay={false}
    >
      <div className="flex h-full flex-col justify-between">
        <div>
          <div className="border-white-50 flex items-center justify-between border-b px-4 py-2.5">
            <div className="body-md-medium-14">Layout</div>
            <CloseIcon onClick={onClose} className="cursor-pointer" />
          </div>

          <div className="p-4">
            <div>
              <div className="heading-sm-medium-16 text-white-500">Style</div>
              <div className="flex items-center justify-between py-4">
                <div className="heading-sm-medium-16">Color reference</div>
                <div className="body-sm-medium-12 flex items-center gap-2">
                  <SettingColorDownUpIcon />
                  Green Up / Red Down
                  <div>
                    <ChevronDownIcon />
                  </div>
                </div>
              </div>
              <div className="flex items-center justify-between py-4">
                <div className="heading-sm-medium-16">Style Settings</div>
                <div className="flex items-center gap-4">
                  <div className="flex items-center gap-2">
                    <div className="h-4 w-4 rounded-[2px] bg-red-400"></div>
                    <div className="h-4 w-4 rounded-[2px] bg-green-500"></div>
                  </div>
                  <div className="body-sm-medium-12">Fresh</div>
                  <div>
                    <ChevronDownIcon />
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div className="p-4">
            <div>
              <div className="heading-sm-medium-16 text-white-500">
                Order Mode
              </div>
              <div className="grid grid-cols-2 gap-2 py-4">
                <div className="flex w-full flex-col items-center gap-2">
                  <div
                    onClick={() => {
                      dispatch(
                        setIsLayoutAdvanced({ isLayoutAdvanced: false })
                      );
                      onClose();
                    }}
                    className={`bg-white-25 flex h-[80px] w-full cursor-pointer items-end justify-center rounded-[8px] border p-1 hover:border-green-500 ${
                      isLayoutAdvanced ? "border-white-100" : "border-green-500"
                    }`}
                  >
                    <div className="bg-white-50 flex gap-[2px] rounded-[6px] p-1 pt-4">
                      <div className="h-1 w-[23px] rounded-[1px] bg-green-500"></div>
                      <div className="h-1 w-[23px] rounded-[1px] bg-red-400"></div>
                    </div>
                  </div>
                  <div className="heading-sm-medium-16">Classic</div>
                </div>

                <div className="flex w-full flex-col items-center gap-2">
                  <div
                    onClick={() => {
                      dispatch(setIsLayoutAdvanced({ isLayoutAdvanced: true }));
                      onClose();
                    }}
                    className={`bg-white-25 flex h-[80px] w-full cursor-pointer justify-end rounded-[8px] border p-1 hover:border-green-500 ${
                      isLayoutAdvanced ? "border-green-500" : "border-white-100"
                    }`}
                  >
                    <div className="bg-white-50 flex h-full w-[40px] flex-col justify-between rounded-[6px] p-1">
                      <div className="flex gap-[2px]">
                        <div className="h-1 w-[23px] rounded-[1px] bg-green-500"></div>
                        <div className="h-1 w-[23px] rounded-[1px] bg-red-400"></div>
                      </div>
                      <div className="h-1 w-full rounded-[1px] bg-green-500"></div>
                    </div>
                  </div>
                  <div className="heading-sm-medium-16">Advanced</div>
                </div>
              </div>
            </div>
          </div>

          <div className="p-4 pb-0">
            <div className="border-white-50 border-t">
              <div className="flex items-center justify-between py-2">
                <div className="heading-sm-medium-16">Favourite</div>
                <AppToggle
                  value={isHasFavourite}
                  onChange={() => setIsHasFavourite(!isHasFavourite)}
                />
              </div>
              <div className="flex items-center justify-between py-2">
                <div className="heading-sm-medium-16">Chart</div>
                <AppToggle
                  value={isHasChart}
                  onChange={() => setIsHasChart(!isHasChart)}
                />
              </div>
              <div className="flex items-center justify-between py-2">
                <div className="heading-sm-medium-16">Order Book</div>
                <AppToggle
                  value={isHasOrderBook}
                  onChange={() => setIsHasOrderBook(!isHasOrderBook)}
                />
              </div>
              <div className="flex items-center justify-between py-2">
                <div className="heading-sm-medium-16">Trades</div>
                <AppToggle
                  value={isHasTrades}
                  onChange={() => setIsHasTrades(!isHasTrades)}
                />
              </div>
              <div className="flex items-center justify-between py-2">
                <div className="heading-sm-medium-16">Open Orders</div>
                <AppToggle
                  value={isHasOpenOrders}
                  onChange={() => setIsHasOpenOrders(!isHasOpenOrders)}
                />
              </div>
              <div className="flex items-center justify-between py-2">
                <div className="heading-sm-medium-16">Place Order</div>
                <AppToggle
                  value={isHasPlaceOrder}
                  onChange={() => setIsHasPlaceOrder(!isHasPlaceOrder)}
                />
              </div>
            </div>
          </div>
        </div>

        <div className="p-4">
          <AppButton
            size="large"
            variant="secondary"
            onClick={() => {
              dispatch(setIsLayoutAdvanced({ isLayoutAdvanced: false }));
              onClose();
            }}
          >
            Back to Default Layout
          </AppButton>
        </div>
      </div>
    </AppDrawer>
  );
};

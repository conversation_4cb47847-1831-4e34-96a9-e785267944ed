import React from "react";
import { BaseModal } from "@/modals/BaseModal";
import { CloseIcon24, SettingColorDownUpIcon } from "@/assets/icons";
import { AppButton } from "@/components";
import { useMediaQuery } from "react-responsive";

export const ModalColorPreference = ({
  isOpen,
  onClose,
}: {
  isOpen: boolean;
  onClose: () => void;
}) => {
  const isMobile = useMediaQuery({ query: "(max-width: 768px)" });

  return (
    <BaseModal
      className="w-screen md:w-[500px]"
      isOpen={isOpen}
      onClose={onClose}
      isBottom={isMobile}
    >
      <div className=" flex justify-between">
        <div className="heading-md-semibold-18">Color Preference</div>
        <div onClick={onClose} className="cursor-pointer">
          <CloseIcon24 />
        </div>
      </div>

      <div className="border-white-100 mt-7 flex cursor-pointer flex-col gap-4 rounded-[12px] border p-4">
        <div className="body-md-regular-14 flex items-center gap-2">
          <SettingColorDownUpIcon /> Green Up / Red Down
        </div>
        <div>
          <div className="flex justify-between">
            <div className="flex items-center gap-2">
              <div className="bg-white-50 h-4 w-4 rounded-full"></div>
              <div className="bg-white-50 h-4 w-[150px] rounded-full"></div>
            </div>

            <div className="body-md-medium-14 text-green-500">+ 4.15%</div>
          </div>

          <div className="flex justify-between">
            <div className="flex items-center gap-2">
              <div className="bg-white-50 h-4 w-4 rounded-full"></div>
              <div className="bg-white-50 h-4 w-[150px] rounded-full"></div>
            </div>

            <div className="body-md-medium-14 text-red-400">- 3.21%</div>
          </div>
        </div>
      </div>

      <div className="border-white-100 mt-4 flex cursor-pointer flex-col gap-4 rounded-[12px] border p-4">
        <div className="body-md-regular-14 flex items-center gap-2">
          <SettingColorDownUpIcon className="rotate-[180deg]" /> Red Up / Green
          Down
        </div>
        <div>
          <div className="flex justify-between">
            <div className="flex items-center gap-2">
              <div className="bg-white-50 h-4 w-4 rounded-full"></div>
              <div className="bg-white-50 h-4 w-[150px] rounded-full"></div>
            </div>

            <div className="body-md-medium-14 text-red-400">+3.21 %</div>
          </div>

          <div className="flex justify-between">
            <div className="flex items-center gap-2">
              <div className="bg-white-50 h-4 w-4 rounded-full"></div>
              <div className="bg-white-50 h-4 w-[150px] rounded-full"></div>
            </div>

            <div className="body-md-medium-14 text-green-500">- 4.15%</div>
          </div>
        </div>
      </div>

      <div className="mt-8 grid grid-cols-2 gap-3">
        <AppButton variant="secondary" size="large" onClick={onClose}>
          Cancel
        </AppButton>
        <AppButton variant="buy" size="large">
          Confirm
        </AppButton>
      </div>
    </BaseModal>
  );
};

import React, { useState } from "react";
import { BaseModal } from "@/modals/BaseModal";
import { CloseIcon24, GoogleIcon } from "@/assets/icons";
import { AppInputCodeVerify } from "@/components";

export const ModalWithdrawVerification = ({
  isOpen,
  onClose,
}: {
  isOpen: boolean;
  onClose: () => void;
}) => {
  const CODE_LENGTH = 6;

  const [values, setValues] = useState<string[]>(Array(CODE_LENGTH).fill(""));

  return (
    <BaseModal
      className="w-screen md:w-[500px]"
      isOpen={isOpen}
      onClose={onClose}
    >
      <div className="flex justify-between">
        <div className="heading-sm-medium-16">Withdraw Verification</div>
        <div onClick={onClose} className="cursor-pointer">
          <CloseIcon24 />
        </div>
      </div>

      <div className="my-2 my-8 flex flex-col items-center gap-2">
        <div className="body-sm-regular-12 text-white-500 flex items-center gap-2">
          <GoogleIcon /> Google Authenticator
        </div>

        <AppInputCodeVerify
          length={CODE_LENGTH}
          values={values}
          setValues={setValues}
        />
      </div>
    </BaseModal>
  );
};

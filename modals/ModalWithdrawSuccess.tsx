import React from "react";
import { BaseModal } from "@/modals/BaseModal";
import { DoneIcon } from "@/assets/icons";
import { AppButton } from "@/components";
import { useMediaQuery } from "react-responsive";

export const ModalWithdrawSuccess = ({
  isOpen,
  onClose,
}: {
  isOpen: boolean;
  onClose: () => void;
}) => {
  const isMobile = useMediaQuery({ query: "(max-width: 768px)" });

  return (
    <BaseModal
      className="w-screen md:w-[500px]"
      isOpen={isOpen}
      onClose={onClose}
      isBottom={isMobile}
    >
      <div className="flex flex-col items-center">
        <DoneIcon />
        <div className="body-md-regular-14 mt-4">Recipient Amount</div>
        <div className="heading-lg-semibold-32 py-2">100 SOL</div>
        <div className="body-sm-regular-12 text-white-500 mb-4">
          Please note that you will receive an email once it is completed
        </div>
      </div>

      <div className="body-md-regular-14 flex justify-between p-4">
        <div className="text-white-500">Network</div>
        <div>Solana</div>
      </div>
      <div className="body-md-regular-14 flex justify-between p-4">
        <div className="text-white-500">Coin</div>
        <div>SOL</div>
      </div>

      <AppButton
        onClick={onClose}
        variant="buy"
        size="large"
        className="mt-6 w-full"
      >
        OK
      </AppButton>
    </BaseModal>
  );
};

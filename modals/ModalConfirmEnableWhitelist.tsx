import { BaseModal } from "@/modals/BaseModal";
import React from "react";
import { AppButton } from "@/components";
import { InforIcon32 } from "@/assets/icons";

export const ModalConfirmEnableWhitelist = ({
  isOpen,
  onClose,
  onConfirm,
  withdrawalWhitelist,
}: {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  withdrawalWhitelist: boolean;
}) => {
  return (
    <BaseModal
      className="w-[343px] lg:w-[500px]"
      isOpen={isOpen}
      onClose={onClose}
    >
      <div className="flex flex-col items-center">
        <InforIcon32 />

        <div className="heading-md-semibold-18 mt-2">
          {withdrawalWhitelist ? "Disabled" : "Enable"} whitelist
        </div>
        <div className="heading-sm-medium-16 text-white-500 mt-2 text-center !font-normal">
          Once this function is enable, your account will only be able to
          withdraw to addresses on your whitelist
        </div>
      </div>

      <div className="mt-6 grid grid-cols-2 gap-3">
        <AppButton size="large" onClick={onClose} variant="secondary">
          Cancel
        </AppButton>
        <AppButton size="large" variant="buy" onClick={onConfirm}>
          Confirm
        </AppButton>
      </div>
    </BaseModal>
  );
};

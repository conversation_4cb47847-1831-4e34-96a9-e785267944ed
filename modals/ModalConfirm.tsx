import { BaseModal } from "@/modals/BaseModal";
import React from "react";
import { AppButton } from "@/components";
import { InforIcon32 } from "@/assets/icons";

export const ModalConfirm = ({
  isOpen,
  onClose,
  description,
  onConfirm,
  titleAction = "Remove",
}: {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  description: string;
  titleAction?: string;
}) => {
  const handleConfirm = async () => {
    try {
      await onConfirm();
    } catch (e) {
      console.error(e);
    } finally {
      onClose();
    }
  };
  return (
    <BaseModal className="w-[300px]" isOpen={isOpen} onClose={onClose}>
      <div className="flex justify-center">
        <InforIcon32 className="text-orange-500" />
      </div>
      <div className={`heading-sm-medium-16 mt-2 text-center !font-normal`}>
        {description}
      </div>
      <div className="mt-6 grid grid-cols-2 gap-3">
        <AppButton size="large" onClick={onClose} variant="secondary">
          Cancel
        </AppButton>
        <AppButton size="large" variant="buy" onClick={handleConfirm}>
          {titleAction}
        </AppButton>
      </div>
    </BaseModal>
  );
};

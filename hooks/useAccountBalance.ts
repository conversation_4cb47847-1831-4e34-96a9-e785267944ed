import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/store";
import { TAccountUpdateWsData, TBalance } from "@/types/account";
import {
  AppBroadcast,
  BROADCAST_EVENTS,
  TBroadcastEvent,
} from "@/libs/broadcast";
import { setAccount } from "@/store/account.store";
import BigNumber from "bignumber.js";

const useAccountBalance = ({ coin }: { coin?: string }) => {
  const balances = useSelector(
    (state: RootState) => state.account.account?.balances
  );

  const dispatch = useDispatch();
  const [accountBalances, setAccountBalances] = useState<TBalance[] | []>([]);
  const [accou]
  const [coinBalance, setCoinBalance] = useState<TBalance>({
    available: "0",
    locked: "0",
    asset: "",
  });
  const tradingPairs = useSelector(
    (state: RootState) => state.tradingPair.tradingPairs
  );

  useEffect(() => {
    const currentBalances = balances || [];

    if (coin) {
      const coinBalance = currentBalances.find(
        (balance) => balance.asset?.toLowerCase() === coin?.toLowerCase()
      );

      if (coinBalance?.asset) {
        setCoinBalance(coinBalance);
      }
    } else {
      setAccountBalances(currentBalances);
    }
  }, [balances, coin]);

  useEffect(() => {
    const handleAccountUpdate = (event: TBroadcastEvent) => {
      const balanceUpdated: TAccountUpdateWsData = JSON.parse(event.detail);

      const currentBalances = balances || [];
      const assetExists = currentBalances.some(
        (item) =>
          item.asset?.toUpperCase() === balanceUpdated.asset?.toUpperCase()
      );

      let accountBalancesUpdated;

      if (assetExists) {
        accountBalancesUpdated = currentBalances.map((item) => {
          const isSameAsset =
            item.asset?.toUpperCase() === balanceUpdated.asset?.toUpperCase();
          const hasValidOperationId =
            !item.operationId || balanceUpdated.operationId > item.operationId;
          const shouldUpdateBalance = isSameAsset && hasValidOperationId;

          return shouldUpdateBalance
            ? {
                ...item,
                available: balanceUpdated.available,
                locked: balanceUpdated.locked,
                operationId: balanceUpdated.operationId,
              }
            : item;
        });
      } else {
        accountBalancesUpdated = [
          ...currentBalances,
          {
            asset: balanceUpdated.asset,
            available: balanceUpdated.available,
            locked: balanceUpdated.locked,
            operationId: balanceUpdated.operationId,
          },
        ];
      }

      dispatch(setAccount({ balances: accountBalancesUpdated }));
    };

    AppBroadcast.on(BROADCAST_EVENTS.ACCOUNT_UPDATED, handleAccountUpdate);

    return () => {
      AppBroadcast.remove(
        BROADCAST_EVENTS.ACCOUNT_UPDATED,
        handleAccountUpdate
      );
    };
  }, [balances, dispatch]);

  useEffect(() => {
    if (!tradingPairs.length) {
      return;
    }

    const accountBalanceWithUsdValue = accountBalances.map((item) => {
      const ticker = tradingPairs.find(
        (t) => t.symbol?.toUpperCase() === `${item.asset?.toUpperCase()}USDT`
      );

      return {
        ...item,
        usdValue: BigNumber(item.available || 0)
          .multipliedBy(ticker?.lastPrice || 0)
          .toFixed(),
      };
    });

    setAccountBalances(accountBalanceWithUsdValue);
  }, [JSON.stringify(tradingPairs)]);

  return {
    accountBalances,
    coinBalance,
  };
};

export default useAccountBalance;

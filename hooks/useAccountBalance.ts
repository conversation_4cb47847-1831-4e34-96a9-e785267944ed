import { useEffect, useState, useMemo, useRef } from "react";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/store";
import { TAccountUpdateWsData, TBalance } from "@/types/account";
import {
  AppBroadcast,
  BROADCAST_EVENTS,
  TBroadcastEvent,
} from "@/libs/broadcast";
import { setAccount } from "@/store/account.store";
import BigNumber from "bignumber.js";
import { TradingPair } from "@/types/pair";

// Helper function to create a stable key for trading pairs comparison
const createTradingPairsKey = (tradingPairs: TradingPair[]): string => {
  return tradingPairs
    .map((pair) => `${pair.symbol}:${pair.lastPrice}`)
    .sort()
    .join("|");
};

// Utility function to calculate USD value for a balance item
const calculateUsdValue = (
  balance: TBalance,
  tradingPairs: TradingPair[]
): string => {
  const asset = balance.asset?.toUpperCase();

  if (asset === "USDT") {
    return BigNumber(balance.available || 0)
      .plus(balance.locked || 0)
      .decimalPlaces(8, BigNumber.ROUND_DOWN)
      .toFixed();
  }

  // Find the trading pair for this asset against USDT
  const ticker = tradingPairs.find(
    (t) => t.symbol?.toUpperCase() === `${asset?.toUpperCase()}USDT`
  );

  if (!ticker?.lastPrice) {
    return "0";
  }

  // Calculate total balance (available + locked) * price
  const totalBalance = BigNumber(balance.available || 0).plus(
    balance.locked || 0
  );
  return totalBalance
    .multipliedBy(ticker.lastPrice)
    .decimalPlaces(8, BigNumber.ROUND_DOWN)
    .toFixed();
};

const useAccountBalance = ({ coin }: { coin?: string }) => {
  const balances = useSelector(
    (state: RootState) => state.account.account?.balances
  );

  const dispatch = useDispatch();
  const [accountBalances, setAccountBalances] = useState<TBalance[] | []>([]);
  const [coinBalance, setCoinBalance] = useState<TBalance>({
    available: "0",
    locked: "0",
    asset: "",
  });
  const tradingPairs = useSelector(
    (state: RootState) => state.tradingPair.tradingPairs
  );

  // Create a stable reference for trading pairs to prevent unnecessary re-renders
  const tradingPairsKey = useMemo(() => {
    return createTradingPairsKey(tradingPairs);
  }, [tradingPairs]);

  // Store previous trading pairs key to detect actual changes
  const prevTradingPairsKeyRef = useRef<string>("");
  const stableTradingPairs = useRef<TradingPair[]>(tradingPairs);

  // Only update stable reference when trading pairs actually change
  if (tradingPairsKey !== prevTradingPairsKeyRef.current) {
    stableTradingPairs.current = tradingPairs;
    prevTradingPairsKeyRef.current = tradingPairsKey;
  }

  useEffect(() => {
    const currentBalances = balances || [];

    if (coin) {
      const coinBalance = currentBalances.find(
        (balance) => balance.asset?.toLowerCase() === coin?.toLowerCase()
      );

      if (coinBalance?.asset) {
        // Calculate USD value for the specific coin balance
        const usdValue = calculateUsdValue(
          coinBalance,
          stableTradingPairs.current
        );
        setCoinBalance({
          ...coinBalance,
          valueInUsd: usdValue,
        });
      }
    } else {
      setAccountBalances(currentBalances);
    }
  }, [balances, coin, tradingPairsKey]);

  useEffect(() => {
    const handleAccountUpdate = (event: TBroadcastEvent) => {
      const balanceUpdated: TAccountUpdateWsData = JSON.parse(event.detail);

      const currentBalances = balances || [];
      const assetExists = currentBalances.some(
        (item) =>
          item.asset?.toUpperCase() === balanceUpdated.asset?.toUpperCase()
      );

      let accountBalancesUpdated;

      if (assetExists) {
        accountBalancesUpdated = currentBalances.map((item) => {
          const isSameAsset =
            item.asset?.toUpperCase() === balanceUpdated.asset?.toUpperCase();
          const hasValidOperationId =
            !item.operationId || balanceUpdated.operationId > item.operationId;
          const shouldUpdateBalance = isSameAsset && hasValidOperationId;

          if (shouldUpdateBalance) {
            const updatedBalance = {
              ...item,
              available: balanceUpdated.available,
              locked: balanceUpdated.locked,
              operationId: balanceUpdated.operationId,
            };
            // Calculate USD value for the updated balance
            const usdValue = calculateUsdValue(
              updatedBalance,
              stableTradingPairs.current
            );
            return {
              ...updatedBalance,
              valueInUsd: usdValue,
            };
          }
          return item;
        });
      } else {
        const newBalance = {
          asset: balanceUpdated.asset,
          available: balanceUpdated.available,
          locked: balanceUpdated.locked,
          operationId: balanceUpdated.operationId,
        };
        // Calculate USD value for the new balance
        const usdValue = calculateUsdValue(
          newBalance,
          stableTradingPairs.current
        );
        accountBalancesUpdated = [
          ...currentBalances,
          {
            ...newBalance,
            valueInUsd: usdValue,
          },
        ];
      }

      dispatch(setAccount({ balances: accountBalancesUpdated }));
    };

    AppBroadcast.on(BROADCAST_EVENTS.ACCOUNT_UPDATED, handleAccountUpdate);

    return () => {
      AppBroadcast.remove(
        BROADCAST_EVENTS.ACCOUNT_UPDATED,
        handleAccountUpdate
      );
    };
  }, [balances, dispatch, tradingPairsKey]);

  // Memoize the calculation based on the raw balances from Redux store
  const accountBalancesWithUsd = useMemo(() => {
    const currentBalances = balances || [];

    if (!stableTradingPairs.current.length || !currentBalances.length) {
      return currentBalances;
    }

    return currentBalances.map((item) => {
      const usdValue = calculateUsdValue(item, stableTradingPairs.current);
      return {
        ...item,
        valueInUsd: usdValue,
      };
    });
    // tradingPairsKey is needed to trigger recalculation when trading pairs change
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [balances, tradingPairsKey]);

  // Update account balances when USD values change
  useEffect(() => {
    if (!coin) {
      setAccountBalances(accountBalancesWithUsd);
    }
  }, [accountBalancesWithUsd, coin]);

  return {
    accountBalances,
    coinBalance,
  };
};

export default useAccountBalance;

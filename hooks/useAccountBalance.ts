import { useEffect, useState, useMemo } from "react";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/store";
import { TAccountUpdateWsData, TBalance } from "@/types/account";
import {
  AppBroadcast,
  BROADCAST_EVENTS,
  TBroadcastEvent,
} from "@/libs/broadcast";
import { setAccount } from "@/store/account.store";
import BigNumber from "bignumber.js";
import { TradingPair } from "@/types/pair";

// Utility function to calculate USD value for a balance item
const calculateUsdValue = (
  balance: TBalance,
  tradingPairs: TradingPair[]
): string => {
  const asset = balance.asset?.toUpperCase();

  if (asset === "USDT") {
    return BigNumber(balance.available || 0)
      .plus(balance.locked || 0)
      .decimalPlaces(8, BigNumber.ROUND_DOWN)
      .toFixed();
  }

  // Find the trading pair for this asset against USDT
  const ticker = tradingPairs.find(
    (t) => t.symbol?.toUpperCase() === `${asset?.toUpperCase()}USDT`
  );

  if (!ticker?.lastPrice) {
    return "0";
  }

  // Calculate total balance (available + locked) * price
  const totalBalance = BigNumber(balance.available || 0).plus(
    balance.locked || 0
  );
  return totalBalance
    .multipliedBy(ticker.lastPrice)
    .decimalPlaces(8, BigNumber.ROUND_DOWN)
    .toFixed();
};

const useAccountBalance = ({ coin }: { coin?: string }) => {
  const balances = useSelector(
    (state: RootState) => state.account.account?.balances
  );

  const dispatch = useDispatch();
  const [accountBalances, setAccountBalances] = useState<TBalance[] | []>([]);
  const [coinBalance, setCoinBalance] = useState<TBalance>({
    available: "0",
    locked: "0",
    asset: "",
  });
  const tradingPairs = useSelector(
    (state: RootState) => state.tradingPair.tradingPairs
  );

  useEffect(() => {
    const currentBalances = balances || [];

    if (coin) {
      const coinBalance = currentBalances.find(
        (balance) => balance.asset?.toLowerCase() === coin?.toLowerCase()
      );

      if (coinBalance?.asset) {
        // Calculate USD value for the specific coin balance
        const usdValue = calculateUsdValue(coinBalance, tradingPairs);
        setCoinBalance({
          ...coinBalance,
          valueInUsd: usdValue,
        });
      }
    } else {
      setAccountBalances(currentBalances);
    }
  }, [balances, coin, tradingPairs]);

  useEffect(() => {
    const handleAccountUpdate = (event: TBroadcastEvent) => {
      const balanceUpdated: TAccountUpdateWsData = JSON.parse(event.detail);

      const currentBalances = balances || [];
      const assetExists = currentBalances.some(
        (item) =>
          item.asset?.toUpperCase() === balanceUpdated.asset?.toUpperCase()
      );

      let accountBalancesUpdated;

      if (assetExists) {
        accountBalancesUpdated = currentBalances.map((item) => {
          const isSameAsset =
            item.asset?.toUpperCase() === balanceUpdated.asset?.toUpperCase();
          const hasValidOperationId =
            !item.operationId || balanceUpdated.operationId > item.operationId;
          const shouldUpdateBalance = isSameAsset && hasValidOperationId;

          if (shouldUpdateBalance) {
            const updatedBalance = {
              ...item,
              available: balanceUpdated.available,
              locked: balanceUpdated.locked,
              operationId: balanceUpdated.operationId,
            };
            // Calculate USD value for the updated balance
            const usdValue = calculateUsdValue(updatedBalance, tradingPairs);
            return {
              ...updatedBalance,
              valueInUsd: usdValue,
            };
          }
          return item;
        });
      } else {
        const newBalance = {
          asset: balanceUpdated.asset,
          available: balanceUpdated.available,
          locked: balanceUpdated.locked,
          operationId: balanceUpdated.operationId,
        };
        // Calculate USD value for the new balance
        const usdValue = calculateUsdValue(newBalance, tradingPairs);
        accountBalancesUpdated = [
          ...currentBalances,
          {
            ...newBalance,
            valueInUsd: usdValue,
          },
        ];
      }

      dispatch(setAccount({ balances: accountBalancesUpdated }));
    };

    AppBroadcast.on(BROADCAST_EVENTS.ACCOUNT_UPDATED, handleAccountUpdate);

    return () => {
      AppBroadcast.remove(
        BROADCAST_EVENTS.ACCOUNT_UPDATED,
        handleAccountUpdate
      );
    };
  }, [balances, dispatch]);

  useEffect(() => {
    if (!tradingPairs.length) {
      return;
    }

    const accountBalanceWithUsdValue = accountBalances.map((item) => {
      const ticker = tradingPairs.find(
        (t) => t.symbol?.toUpperCase() === `${item.asset?.toUpperCase()}USDT`
      );

      return {
        ...item,
        usdValue: BigNumber(item.available || 0)
          .multipliedBy(ticker?.lastPrice || 0)
          .toFixed(),
      };
    });

    setAccountBalances(accountBalanceWithUsdValue);
  }, [JSON.stringify(tradingPairs)]);

  return {
    accountBalances,
    coinBalance,
  };
};

export default useAccountBalance;

import { TradingPair } from "@/types/pair";
import { createSlice, PayloadAction } from "@reduxjs/toolkit";

const defaultTradingPair: TradingPair = {
  lastPrice: null,
  highPrice: null,
  lowPrice: null,
  priceChangePercent: null,
  priceChange: null,
  baseVolume: null,
  quoteVolume: null,
  timestamp: null,
  isUp: false,
  isUp24h: false,
  symbol: "",
};

interface TradingState {
  tradingPair: TradingPair;
  tradingPairs: TradingPair[];
}

const initialState: TradingState = {
  tradingPair: defaultTradingPair,
  tradingPairs: [],
};

export const tradingPairSlice = createSlice({
  name: "tradingPair",
  initialState,
  reducers: {
    setTradingPair: (state, action: PayloadAction<TradingPair>) => {
      state.tradingPair = action.payload;
    },
    setTradingPairs: (state, action: PayloadAction<TradingPair[]>) => {
      state.tradingPairs = action.payload;
    },
  },
});

export const { setTradingPair, setTradingPairs } = tradingPairSlice.actions;

export default tradingPairSlice.reducer;

"use client";

import React, { ReactNode, useCallback, useEffect } from "react";
import { <PERSON><PERSON>, Sidebar } from "@/layouts";
import {
  AddUserIcon,
  HomeIcon,
  OrderHistoryIcon,
  ProfileIcon,
  SettingIcon,
  WalletIcon,
} from "@/assets/icons";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/store";
import {
  getArrTickerRoom,
  subscribeSocketChannel,
  unsubscribeSocketChannel,
} from "@/libs/socket";
import {
  AppBroadcast,
  BROADCAST_EVENTS,
  TBroadcastEvent,
} from "@/libs/broadcast";
import { formatTickersUpdate } from "@/utils/format";
import { TTickerUpdate } from "@/components/PairMarket/services/TickerHandler";
import { setTradingPairs } from "@/store/tradingPair.store";

const MENUS = [
  {
    name: "Dashboard",
    path: "/my/dashboard",
    icon: <HomeIcon />,
  },
  {
    name: "Assets",
    icon: <WalletIcon />,
    path: "/my/overview",
    // sub: [
    //   {
    //     name: "Overview",
    //     path: "/my/overview",
    //   },
    //   {
    //     name: "Spot",
    //     path: "#",
    //   },
    // ],
  },
  {
    name: "Orders",
    icon: <OrderHistoryIcon />,
    sub: [
      {
        name: "Spot Orders",
        path: "/my/orders-exchange",
      },
      {
        name: "Transaction History",
        path: "/my/transaction-history",
      },
      // {
      //   name: "Convert History",
      //   path: "/my/convert-history",
      // },
    ],
  },
  {
    name: "Referral",
    icon: <AddUserIcon />,
    path: "/my/referral",
  },
  {
    name: "Account",
    icon: <ProfileIcon />,
    sub: [
      {
        name: "Identification",
        path: "/my/identification",
      },
      {
        name: "Security",
        path: "/my/security",
      },
      // {
      //   name: "Payment",
      //   path: "/my/payment",
      // },
      {
        name: "API Management",
        path: "#",
      },
      // {
      //   name: "Account Statement",
      //   path: "#",
      // },
      // {
      //   name: "Financial Reports",
      //   path: "/my/financial-reports",
      // },
    ],
  },
  {
    name: "Settings",
    icon: <SettingIcon />,
    path: "/my/settings",
  },
];

export default function Layout({ children }: { children: ReactNode }) {
  const socketConnected = useSelector(
    (state: RootState) => state.metadata.socketConnected
  );
  const dispatch = useDispatch();

  const handleArrTickersUpdate = useCallback(
    (data: TBroadcastEvent) => {
      const tickersUpdated = formatTickersUpdate(
        JSON.parse(data.detail) as TTickerUpdate[]
      );

      dispatch(setTradingPairs(tickersUpdated));
    },
    [dispatch]
  );

  // Subscribe to tickers updates
  useEffect(() => {
    if (!socketConnected) return;
    subscribeSocketChannel({
      params: [getArrTickerRoom()],
    });

    AppBroadcast.on(
      BROADCAST_EVENTS.ARR_TICKERS_UPDATED,
      handleArrTickersUpdate
    );

    return () => {
      // Clean up socket and event listeners
      unsubscribeSocketChannel({
        params: [getArrTickerRoom()],
      });

      AppBroadcast.remove(
        BROADCAST_EVENTS.ARR_TICKERS_UPDATED,
        handleArrTickersUpdate
      );
    };
  }, [handleArrTickersUpdate, socketConnected]);

  return (
    <div>
      <Header />
      <div className="bg-black-900 min-h-screen pt-[50px]">
        <div className="mx-auto flex w-full max-w-[1440px] flex-col lg:flex-row lg:gap-6 lg:px-10 lg:py-6">
          <Sidebar menus={MENUS} />
          <div className="lg:flex-1">{children}</div>
        </div>
      </div>
    </div>
  );
}

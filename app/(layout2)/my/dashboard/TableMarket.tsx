"use client";

import React, { memo, useEffect, useState } from "react";
import { AppButtonSort, AppButton } from "@/components";
import { AppNumber } from "@/components/AppNumber";
import { useMediaQuery } from "react-responsive";
import Link from "next/link";
import { useSelector } from "react-redux";
import { RootState } from "@/store/index";
import { TAsset } from "@/types/asset";
import _ from "lodash";
import useAccountBalance from "@/hooks/useAccountBalance";
import { TBalance } from "@/types/account";
import BigNumber from "bignumber.js";

const CoinItem = ({ coin }: { coin: TAsset }) => {
  const isMobile = useMediaQuery({ query: "(max-width: 992px)" });

  return (
    <div className="border-white-50 hover:bg-white-50 flex w-full items-center lg:border-b">
      <div className="w-[40%] px-2 py-2.5 text-left lg:w-[20%] lg:min-w-[184px]">
        <div className="flex flex-col justify-end">
          <div className="body-sm-regular-12">{coin.symbol}</div>
          <div className="body-xs-regular-10 text-white-500">{coin.name}</div>
        </div>
      </div>

      <div className="body-sm-regular-12 hidden w-[30%] px-2 py-2.5 lg:block lg:w-[20%] lg:min-w-[184px]">
        <div className="flex w-full flex-col items-end">
          <div className="body-sm-regular-12">
            <AppNumber value={coin.available} isFormatLargeNumber={false} />
          </div>
          <div className="body-xs-regular-10 text-white-500">
            <AppNumber
              isForUSD
              value={coin.usdValue}
              isFormatLargeNumber={false}
            />
          </div>
        </div>
      </div>

      <div className="body-sm-regular-12 w-[30%] px-2 py-2.5 lg:w-[20%] lg:min-w-[184px]">
        <div className="flex w-full flex-col items-end">
          <div className="body-sm-regular-12">
            {coin.symbol === "USDT" ? (
              "$1"
            ) : (
              <AppNumber
                value={coin.price}
                isFormatLargeNumber={false}
                isForUSD
              />
            )}
          </div>
          <div className="body-xs-regular-10 text-white-500">--</div>
        </div>
      </div>

      <div className="body-sm-regular-12 w-[30%] px-2 py-2.5 lg:w-[20%] lg:min-w-[184px]">
        {isMobile ? (
          <AppButton size="medium" variant={"buy"}>
            --
          </AppButton>
        ) : (
          <div
            className="text-right"
            style={{
              color: BigNumber(coin?.change24h || 0).isGreaterThanOrEqualTo(0)
                ? "var(--color-green-500)"
                : "var(--color-red-500)",
            }}
          >
            <div className="flex justify-end">
              <AppNumber
                value={coin?.change24h || 0}
                isFormatLargeNumber={false}
              />{" "}
              %
            </div>
          </div>
        )}
      </div>
      <div className="hidden w-[20%] justify-end px-2 py-2.5 md:min-w-[184px] lg:flex">
        <Link href="#" className="body-sm-medium-12 underline">
          Trade
        </Link>
      </div>
    </div>
  );
};

CoinItem.displayName = "CoinItem";

export const TableMarkets = memo(() => {
  const [sortBy, setSortBy] = useState<string>("");
  const [sortType, setSortType] = useState<string>("");
  const [isMounted, setIsMounted] = useState<boolean>(false);
  const assets = useSelector((state: RootState) => state.metadata.assets);
  const [tokensShow, setTokensShow] = useState<TAsset[]>([]);
  const { accountBalances } = useAccountBalance({});
  const tradingPairs = useSelector(
    (state: RootState) => state.tradingPair.tradingPairs
  );

  useEffect(() => {
    setIsMounted(true);
  }, []);

  useEffect(() => {
    let dataToken = assets.map((a: TAsset) => {
      const coinAvailable = accountBalances.find(
        (c: TBalance) => c.asset === a.symbol
      );
      const coinPrice = tradingPairs.find(
        (t) => t.symbol?.toUpperCase() === `${a.symbol?.toUpperCase()}USDT`
      );
      return {
        ...a,
        available: coinAvailable?.available || "0",
        usdValue: coinAvailable?.usdValue || "0",
        price: coinPrice?.lastPrice?.toString() || undefined,
        change24h: coinPrice?.priceChangePercent || "0",
      };
    });

    if (sortType === "desc") {
      dataToken = _.orderBy(
        dataToken,
        [
          (coin: any) => {
            if (sortBy === "available") {
              return Number(coin[sortBy]);
            }
            return coin[sortBy];
          },
        ],
        ["desc"]
      );
    }

    if (sortType === "asc") {
      dataToken = _.orderBy(
        dataToken,
        [
          (coin: any) => {
            if (sortBy === "available") {
              return Number(coin[sortBy]);
            }
            return coin[sortBy];
          },
        ],
        ["asc"]
      );
    }

    setTokensShow(dataToken);
  }, [accountBalances, assets, sortBy, sortType, JSON.stringify(tradingPairs)]);

  if (!isMounted) return <></>;

  return (
    <>
      <div className="w-full">
        <div className="flex w-full items-center md:hidden">
          <div className="body-sm-regular-12 text-white-500 flex w-[40%] items-center px-2 py-1.5 ">
            Name
          </div>
          <div className="body-sm-regular-12 text-white-500 w-[30%] px-2 py-1.5 text-left ">
            <div className="flex items-center justify-end">Last price</div>
          </div>
          <div className="body-sm-regular-12 text-white-500 w-[30%] px-2 py-1.5 text-left">
            <div className="flex items-center justify-end gap-2">
              Change (%)
            </div>
          </div>
        </div>

        <div className="hidden w-full items-center md:flex">
          <div className="body-sm-regular-12 text-white-500 flex w-[20%] items-center px-2 py-1.5 md:min-w-[184px] ">
            <div className="flex items-center gap-2">
              Coin
              <AppButtonSort
                value="symbol"
                sortBy={sortBy}
                sortType={sortType}
                setSortType={setSortType}
                setSortBy={setSortBy}
              />
            </div>
          </div>
          <div className="body-sm-regular-12 text-white-500 w-[20%] px-2 py-1.5 text-left md:min-w-[184px]">
            <div className="flex items-center justify-end gap-2">
              Amount
              <AppButtonSort
                value="available"
                sortBy={sortBy}
                sortType={sortType}
                setSortType={setSortType}
                setSortBy={setSortBy}
              />
            </div>
          </div>
          <div className="body-sm-regular-12 text-white-500 w-[20%] px-2 py-1.5 text-left md:min-w-[184px]">
            <div className="flex items-center justify-end gap-2">
              Coin Price/ Cost Price
              <AppButtonSort
                value="price"
                sortBy={sortBy}
                sortType={sortType}
                setSortType={setSortType}
                setSortBy={setSortBy}
              />
            </div>
          </div>
          <div className="body-sm-regular-12 text-white-500 w-[20%] px-2 py-1.5 text-left md:min-w-[184px]">
            <div className="flex items-center justify-end gap-2">
              24h Change
              <AppButtonSort
                value="24hChange"
                sortBy={sortBy}
                sortType={sortType}
                setSortType={setSortType}
                setSortBy={setSortBy}
              />
            </div>
          </div>
          <div className="body-sm-regular-12 text-white-500 w-[20%] px-2 py-1.5 text-right md:min-w-[184px]">
            Trade
          </div>
        </div>

        <div>
          {tokensShow.map((item: TAsset, index: number) => {
            return <CoinItem key={index} coin={item} />;
          })}
        </div>
      </div>
    </>
  );
});

TableMarkets.displayName = "TableMarkets";

"use client";

import React from "react";
import { AppButton } from "@/components";
import { RefreshIcon } from "@/assets/icons";

export default function FinancialReportsPage() {
  return (
    <div className="border-white-100 flex flex-col items-center justify-between gap-4 rounded-[16px] p-4 lg:flex-row lg:border">
      <div className="text-white-500">
        <div className="heading-lg-medium-24 lg:text-white-500 text-white-1000">
          Financial Report
        </div>
        <div className="body-md-regular-14 mt-2">
          There are no documents generated for you at this time.
        </div>
      </div>

      <AppButton
        variant="secondary"
        size="large"
        className="flex h-max items-center gap-2"
      >
        <RefreshIcon /> Refresh
      </AppButton>
    </div>
  );
}

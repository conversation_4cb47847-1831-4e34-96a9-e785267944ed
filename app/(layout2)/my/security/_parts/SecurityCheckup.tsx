"use client";

import React from "react";
import { CloseCircleIcon } from "@/assets/icons";

const SecurityCheckup = () => {
  return (
    <div className="border-white-100 border-b p-4 lg:rounded-[16px] lg:border">
      <div className="heading-lg-medium-24 lg:text-white-500 text-[16px] lg:text-[24px]">
        Security Checkup
      </div>

      <div className="mt-4 flex flex-col gap-4 lg:flex-row lg:gap-8">
        <div className="flex items-center gap-2">
          <CloseCircleIcon />
          Two-Factor Authentication (2FA)
        </div>
        <div className="flex items-center gap-2">
          <CloseCircleIcon />
          Identity Verification
        </div>
        <div className="flex items-center gap-2">
          <CloseCircleIcon />
          Withdrawal Whitelist
        </div>
      </div>
    </div>
  );
};

export default SecurityCheckup;

"use client";

import React from "react";
import { AppButton } from "@/components";
import { MyDevicesIcon, AccountActivityIcon } from "@/assets/icons";

const DevicesAndActivities = () => {
  return (
    <div className="border-white-100 border-b p-4 lg:rounded-[16px] lg:border">
      <div className="heading-lg-medium-24 lg:text-white-500 text-[16px] lg:text-[24px]">
        Devices and Activities
      </div>
      <div className="mt-5 flex flex-col justify-between gap-3 lg:flex-row lg:items-center">
        <div className="flex gap-4 lg:items-center">
          <MyDevicesIcon />
          <div className="flex-1">
            <div className="heading-sm-medium-16 mb-1 text-[14px] lg:text-[16px]">
              My Devices
            </div>
            <div className="body-md-regular-14 text-white-500">
              Manage devices that have login status, and view your device
              history.
            </div>
          </div>
        </div>

        <div className="flex justify-end gap-2">
          <AppButton variant="secondary" className="w-[80px]">
            Manage
          </AppButton>
        </div>
      </div>

      <div className="border-white-100 mt-4 flex flex-col justify-between gap-3 border-t pt-4 lg:flex-row lg:items-center lg:border-0">
        <div className="flex gap-4 lg:items-center">
          <AccountActivityIcon />
          <div className="flex-1">
            <div className="heading-sm-medium-16 mb-1 text-[14px] lg:text-[16px]">
              Account Activity
            </div>
            <div className="body-md-regular-14 text-white-500">
              ULast login: 2025-05-05 09:19:52
              <br />
              Suspicious account activity?
            </div>

            <div className="body-md-medium-14 text-brand-500 mt-1 cursor-pointer underline">
              Disable Account
            </div>
          </div>
        </div>
        <div className="flex justify-end gap-2">
          <AppButton variant="secondary" className="w-[80px]">
            More
          </AppButton>
        </div>
      </div>
    </div>
  );
};

export default DevicesAndActivities;

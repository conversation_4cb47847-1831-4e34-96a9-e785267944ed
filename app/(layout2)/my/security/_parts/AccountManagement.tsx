"use client";

import React from "react";
import { AppButton } from "@/components";
import { DeleteAccountIcon, DisableAccount } from "@/assets/icons";

const AccountManagement = () => {
  return (
    <div className="border-white-100 border-b p-4 lg:rounded-[16px] lg:border">
      <div className="heading-lg-medium-24 lg:text-white-500 text-[16px] lg:text-[24px]">
        Account Management
      </div>
      <div className="mt-5 flex flex-col justify-between gap-3 lg:flex-row lg:items-center">
        <div className="flex gap-4 lg:items-center">
          <DisableAccount />
          <div className="flex-1">
            <div className="heading-sm-medium-16 mb-1 text-[14px] lg:text-[16px]">
              Disable Account
            </div>
            <div className="body-md-regular-14 text-white-500 max-w-[510px]">
              Once the account is disabled, most of your actions will be
              restricted, such as logging in and trading. You can choose to
              unblock the account at any time. This action will not delete your
              account.
            </div>
          </div>
        </div>

        <div className="flex justify-end gap-2">
          <AppButton variant="secondary" className="w-[80px]">
            Manage
          </AppButton>
        </div>
      </div>

      <div className="border-white-100 mt-4 flex flex-col justify-between gap-3 border-t pt-4 lg:flex-row lg:items-center lg:border-0">
        <div className="flex gap-4 lg:items-center">
          <DeleteAccountIcon />
          <div className="flex-1">
            <div className="heading-sm-medium-16 mb-1 text-[14px] lg:text-[16px]">
              Delete account
            </div>
            <div className="body-md-regular-14 text-white-500 max-w-[510px]">
              Please note that account deletion is irreversible. Once deleted,
              you will not be able to access your account or view your
              transaction history.
            </div>
          </div>
        </div>
        <div className="flex justify-end gap-2">
          <AppButton variant="secondary" className="w-[80px]">
            More
          </AppButton>
        </div>
      </div>
    </div>
  );
};

export default AccountManagement;

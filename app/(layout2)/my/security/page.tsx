"use client";

import React from "react";

import SecurityCheckup from "./_parts/SecurityCheckup";
import TwoFactorAuthentication from "./_parts/TwoFactorAuthentication";
import DevicesAndActivities from "./_parts/DevicesAndActivities";
import AccountManagement from "./_parts/AccountManagement";

const MySecurityPage = () => {
  return (
    <div className="flex flex-col lg:gap-8">
      <SecurityCheckup />
      <TwoFactorAuthentication />
      <DevicesAndActivities />
      <AccountManagement />
    </div>
  );
};

export default MySecurityPage;

"use client";

import React, { useState } from "react";
import { App<PERSON><PERSON>on, AppToggle } from "@/components";
import { SettingColorDownUpIcon } from "@/assets/icons";
import { ModalSettingTimezone, ModalColorPreference } from "@/modals";

const SettingsReferences = () => {
  const [isShortCut, setIsShortCut] = useState<boolean>(false);
  const [isShowModalSettingTimezone, setIsShowModalSettingTimezone] =
    useState<boolean>(false);

  const [isShowModalColorPreference, setIsShowModalColorPreference] =
    useState<boolean>(false);

  return (
    <div className="border-white-100 border-b p-4 lg:rounded-[16px] lg:border">
      <div className="heading-lg-medium-24 text-white-500 text-[18px] lg:text-[24px]">
        References
      </div>

      <div className="my-4 flex items-center justify-between">
        <div className="heading-sm-medium-16 mb-1">Color reference</div>

        <div className="flex items-center gap-2">
          <div className="body-sm-medium-12 flex items-center gap-2">
            <SettingColorDownUpIcon />
            Green Up / Red Down
          </div>
          <AppButton
            variant="secondary"
            className="h-max w-[80px]"
            onClick={() => setIsShowModalColorPreference(true)}
          >
            Edit
          </AppButton>
        </div>
      </div>

      <div className="mt-8 flex items-center justify-between">
        <div className="heading-sm-medium-16 mb-1">Style Settings</div>

        <div className="flex items-center gap-2">
          <div className="flex items-center gap-2">
            <div className="h-4 w-4 rounded-[2px] bg-red-400"></div>
            <div className="h-4 w-4 rounded-[2px] bg-green-500"></div>
            <div className="body-sm-medium-12 p-2">Fresh</div>
          </div>
          <AppButton variant="secondary" className="h-max w-[80px]">
            Edit
          </AppButton>
        </div>
      </div>

      <div className="mt-8 flex items-center justify-between">
        <div className="heading-sm-medium-16 mb-1">UTC Time Zone</div>

        <div className="flex items-center gap-2">
          <div className="body-sm-medium-12">Last 24 hours</div>
          <AppButton
            variant="secondary"
            className="h-max w-[80px]"
            onClick={() => setIsShowModalSettingTimezone(true)}
          >
            Edit
          </AppButton>
        </div>
      </div>

      <div className="mt-8 flex items-center justify-between">
        <div className="heading-sm-medium-16 mb-1">Shortcuts</div>

        <div className="flex items-center gap-2">
          <AppToggle
            value={isShortCut}
            onChange={() => setIsShortCut(!isShortCut)}
          />
          <AppButton variant="secondary" className="h-max w-[80px]">
            Edit
          </AppButton>
        </div>
      </div>

      {isShowModalSettingTimezone && (
        <ModalSettingTimezone
          isOpen={isShowModalSettingTimezone}
          onClose={() => setIsShowModalSettingTimezone(false)}
        />
      )}

      {isShowModalColorPreference && (
        <ModalColorPreference
          isOpen={isShowModalColorPreference}
          onClose={() => setIsShowModalColorPreference(false)}
        />
      )}
    </div>
  );
};

export default SettingsReferences;

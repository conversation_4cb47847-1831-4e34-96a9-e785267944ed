"use client";

import React, { useState } from "react";
import { App<PERSON><PERSON>on, AppToggle } from "@/components";
import { ModalConfirmEnableWhitelist } from "../../../../../modals/ModalConfirmEnableWhitelist";

const SettingsWithdrawal = () => {
  const [withdrawalWhitelist, setWithdrawalWhitelist] =
    useState<boolean>(false);
  const [oneStepWithdrawal, setOneStepWithdrawal] = useState<boolean>(false);
  const [
    isShowModalConfirmWithdrawalWhitelist,
    setIsShowModalConfirmWithdrawalWhitelist,
  ] = useState<boolean>(false);

  return (
    <div className="border-white-100 border-b p-4 lg:rounded-[16px] lg:border">
      <div className="heading-lg-medium-24 text-white-500 text-[18px] lg:text-[24px]">
        Withdrawal
      </div>

      <div className="my-4 flex flex-col justify-between gap-4 lg:flex-row lg:items-center">
        <div>
          <div className="heading-sm-medium-16 mb-1">Withdrawal Whitelist</div>
          <div className="body-md-regular-14 text-white-500 max-w-[530px]">
            Once this function is enabled, your account will only be able to
            withdraw to addresses on your whitelist
            <br />
            <a
              href="#"
              target="_blank"
              className="text-brand-500 body-md-medium-14"
            >
              Address Management
            </a>
          </div>
        </div>

        <div className="flex justify-end">
          <AppToggle
            value={withdrawalWhitelist}
            onChange={() => setIsShowModalConfirmWithdrawalWhitelist(true)}
          />
        </div>
      </div>

      <div className="mt-8 flex flex-col justify-between gap-4 lg:flex-row lg:items-center">
        <div>
          <div className="heading-sm-medium-16 mb-1">One-Step Withdrawal</div>
          <div className="body-md-regular-14 text-white-500 max-w-[530px]">
            When this function is turned on, you can withdraw small amount
            crypto to whitelisted addresses without passing 2FA verification
          </div>
        </div>
        <div className="flex justify-end">
          <AppToggle
            value={oneStepWithdrawal}
            onChange={() => setOneStepWithdrawal(!oneStepWithdrawal)}
          />
        </div>
      </div>

      <div className="mt-8 flex flex-col justify-between gap-4 lg:flex-row lg:items-center">
        <div>
          <div className="heading-sm-medium-16 mb-1">Withdraw Setting</div>
          <div className="body-md-regular-14 text-white-500 max-w-[530px]">
            Choose to withdraw through on-chain or off-chain transfer for
            applicable addresses.
          </div>
        </div>

        <div className="flex items-center justify-end gap-2">
          <div className="body-sm-medium-12">Off-Chain withdrawal</div>
          <AppButton variant="secondary" className="h-max w-[80px]">
            Edit
          </AppButton>
        </div>
      </div>

      {isShowModalConfirmWithdrawalWhitelist && (
        <ModalConfirmEnableWhitelist
          withdrawalWhitelist={withdrawalWhitelist}
          isOpen={isShowModalConfirmWithdrawalWhitelist}
          onClose={() => setIsShowModalConfirmWithdrawalWhitelist(false)}
          onConfirm={() => {
            setWithdrawalWhitelist(!withdrawalWhitelist);
            setIsShowModalConfirmWithdrawalWhitelist(false);
          }}
        />
      )}
    </div>
  );
};

export default SettingsWithdrawal;

"use client";

import React from "react";
import { AppButton } from "@/components";

const SettingsPrivacy = () => {
  return (
    <div className="border-white-100 rounded-[16px] p-4 lg:border">
      <div className="heading-lg-medium-24 text-white-500 text-[18px] lg:text-[24px]">
        Privacy
      </div>

      <div className="my-4 flex flex-col justify-between gap-4 lg:flex-row">
        <div>
          <div className="heading-sm-medium-16 mb-1">
            Download Personal Data
          </div>
          <div className="body-md-regular-14 text-white-500 max-w-[530px]">
            The download includes profile, withdrawal, deposit, and trading
            history data.
            <br />
             *For KYC documents, contact the{" "}
            <a
              href="#"
              target="_blank"
              className="text-brand-500 body-md-medium-14"
            >
              DPO team
            </a>
          </div>
        </div>

        <div className="flex justify-end">
          <AppButton variant="secondary" className="h-max w-[80px]">
            Download
          </AppButton>
        </div>
      </div>

      <div className="mt-8 flex justify-between">
        <div className="heading-sm-medium-16 mb-1">Delete Account</div>

        <AppButton variant="secondary" className="h-max w-[80px]">
          Delete
        </AppButton>
      </div>

      <div className="mt-8 flex justify-between">
        <div>
          <div className="heading-sm-medium-16 mb-1">Privacy Portal</div>
          <div className="body-md-regular-14 text-white-500 max-w-[530px]">
            Link your X Account to VDAX
          </div>
        </div>

        <AppButton variant="secondary" className="h-max w-[80px]">
          Enter
        </AppButton>
      </div>
    </div>
  );
};

export default SettingsPrivacy;

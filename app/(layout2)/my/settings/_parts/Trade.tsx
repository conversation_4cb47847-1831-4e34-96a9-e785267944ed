"use client";

import React from "react";
import { AppButton } from "@/components";

const SettingsTrade = () => {
  return (
    <div className="border-white-100 border-b p-4 lg:rounded-[16px] lg:border">
      <div className="heading-lg-medium-24 text-white-500 text-[18px] lg:text-[24px]">
        Trade
      </div>

      <div className="my-4 flex flex-col justify-between gap-4 lg:flex-row">
        <div>
          <div className="heading-sm-medium-16 mb-1">
            Order Confirmation Reminders
          </div>
          <div className="body-md-regular-14 text-white-500 max-w-[530px]">
            If the order reminder function is enabled, it will need to be
            reconfirmed every time an order is submitted.
          </div>
        </div>

        <div className="flex items-center justify-end gap-2">
          <div>
            <div className="body-sm-medium-12">Stop-Limit Order</div>
          </div>
          <AppButton variant="secondary" className="w-[80px]">
            Manage
          </AppButton>
        </div>
      </div>

      <div className="mt-8 flex flex-col justify-between gap-4 lg:flex-row">
        <div>
          <div className="heading-sm-medium-16 mb-1">Fee Reduction</div>
          <div className="body-md-regular-14 text-white-500 max-w-[530px]">
            Use VDAX Token to pay fee
          </div>
        </div>

        <div className="flex items-center justify-end gap-2">
          <div>
            <div className="body-sm-medium-12">Spot fee</div>
          </div>
          <AppButton variant="secondary" className="w-[80px]">
            Manage
          </AppButton>
        </div>
      </div>
    </div>
  );
};

export default SettingsTrade;

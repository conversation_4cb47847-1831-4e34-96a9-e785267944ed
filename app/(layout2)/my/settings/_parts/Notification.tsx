"use client";

import React, { useState } from "react";
import { AppButton } from "@/components";
import {
  ModalAutoPriceAlert,
  ModalNotificationPreference,
  ModalNotificationLanguage,
} from "@/modals";

const SettingsNotification = () => {
  const [isShowModalAutoPriceAlert, setIsShowModalAutoPriceAlert] =
    useState<boolean>(false);
  const [isShowModalNotificationPreferences, setIsShowNotificationPreferences] =
    useState<boolean>(false);
  const [isShowModalNotificationLanguage, setIsShowNotificationLanguage] =
    useState<boolean>(false);

  const [isNotification, setIsNotification] = useState<boolean>(false);
  const [isNotificationSound, setIsNotificationSound] =
    useState<boolean>(false);

  return (
    <div className="border-white-100 border-b p-4 lg:rounded-[16px] lg:border">
      <div className="heading-lg-medium-24 text-white-500 text-[18px] lg:text-[24px]">
        Notifications
      </div>

      <div className="my-4 flex flex-col justify-between gap-4 lg:flex-row">
        <div>
          <div className="heading-sm-medium-16 mb-1">Notification Language</div>
          <div className="body-md-regular-14 text-white-500 max-w-[530px]">
            This will affect the language settings of E-mail and App push.
          </div>
        </div>

        <div className="flex items-center justify-end gap-2">
          <div>
            <div className="body-sm-medium-12">Default</div>
          </div>
          <AppButton
            variant="secondary"
            className="w-[80px]"
            onClick={() => setIsShowNotificationLanguage(true)}
          >
            Edit
          </AppButton>
        </div>
      </div>

      <div className="mt-8 flex flex-col justify-between gap-4 lg:flex-row">
        <div>
          <div className="heading-sm-medium-16 mb-1">
            Notification Preferences
          </div>
          <div className="body-md-regular-14 text-white-500 max-w-[530px]">
            Once configured, you will receive relevant on-site inbox
            notifications within the app and website.
          </div>
        </div>

        <div className="flex items-center justify-end gap-2">
          <div>
            <div className="body-sm-medium-12 text-right">
              Activities, Trade Notification, VDAX News, System Messages
            </div>
          </div>
          <AppButton
            variant="secondary"
            className="w-[80px]"
            onClick={() => setIsShowNotificationPreferences(true)}
          >
            Manage
          </AppButton>
        </div>
      </div>
      <div className="mt-8 flex flex-col justify-between gap-4 lg:flex-row">
        <div>
          <div className="heading-sm-medium-16 mb-1">Auto Price Alert</div>
          <div className="body-md-regular-14 text-white-500 max-w-[530px]">
            Once configured, you will receive alerts on the price changes of
            major and holding cryptos.
          </div>
        </div>

        <div className="flex items-center justify-end gap-2">
          <div>
            <div className="body-sm-medium-12">
              Notifications {isNotification ? "On" : "Off"}, Sound{" "}
              {isNotificationSound ? "On" : "Off"}
            </div>
          </div>
          <AppButton
            variant="secondary"
            className="w-[80px]"
            onClick={() => setIsShowModalAutoPriceAlert(true)}
          >
            Manage
          </AppButton>
        </div>
      </div>

      {isShowModalAutoPriceAlert && (
        <ModalAutoPriceAlert
          setIsNotification={setIsNotification}
          setIsNotificationSound={setIsNotificationSound}
          isNotification={isNotification}
          isNotificationSound={isNotificationSound}
          isOpen={isShowModalAutoPriceAlert}
          onClose={() => setIsShowModalAutoPriceAlert(false)}
        />
      )}

      {isShowModalNotificationPreferences && (
        <ModalNotificationPreference
          isOpen={isShowModalNotificationPreferences}
          onClose={() => setIsShowNotificationPreferences(false)}
        />
      )}
      {isShowModalNotificationLanguage && (
        <ModalNotificationLanguage
          isOpen={isShowModalNotificationLanguage}
          onClose={() => setIsShowNotificationLanguage(false)}
        />
      )}
    </div>
  );
};

export default SettingsNotification;

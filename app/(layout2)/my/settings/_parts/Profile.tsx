"use client";

import React, { useState } from "react";
import { AppButton } from "@/components";
import { EditIcon } from "@/assets/icons";
import { useSelector } from "react-redux";
import { RootState } from "@/store/index";
import Image from "next/image";
import { ModalEditProfile } from "@/modals";

const SettingsProfile = () => {
  const [isShowModalEditProfile, setIsShowModalEditProfile] =
    useState<boolean>(false);
  const userInfo = useSelector((state: RootState) => state.user.userInfo);

  return (
    <div className="border-white-100 border-b p-4 lg:rounded-[16px] lg:border">
      <div className="heading-lg-medium-24 text-white-500 text-[18px] lg:text-[24px]">
        Profile
      </div>

      <div className="my-4 flex flex-col justify-between gap-4 lg:flex-row">
        <div>
          <div className="heading-sm-medium-16 mb-1">Nickname & Avatar</div>
          <div className="body-md-regular-14 text-white-500 max-w-[530px]">
            Set up an avatar and nickname, it is suggested not to use your real
            name or the name of your social account as a nickname.
          </div>
        </div>

        <div className="flex items-center justify-end gap-2">
          <div className="flex items-center gap-2">
            {userInfo.avatar ? (
              <img
                src={userInfo.avatar}
                alt="avatar"
                className="aspect-square h-[32px] !w-[32px] rounded-full"
              />
            ) : (
              <Image
                src={"/images/AvatarDefault.png"}
                alt="avatar"
                width={32}
                height={32}
                className="aspect-square h-[32px] !w-[32px] rounded-full"
              />
            )}
            <div className="body-md-medium-14">{userInfo?.name}</div>
          </div>
          <AppButton
            variant="secondary"
            className="w-[80px] items-center gap-1"
            onClick={() => setIsShowModalEditProfile(true)}
          >
            <EditIcon /> Edit
          </AppButton>
        </div>
      </div>

      <div className=" mt-8 flex flex-col justify-between gap-4 lg:flex-row">
        <div>
          <div className="heading-sm-medium-16 mb-1">C2C Profile</div>
          <div className="body-md-regular-14 text-white-500 max-w-[530px]">
            Edit your C2C nickname, manage your payment methods and the list of
            users you follow.
          </div>
        </div>

        <div className="flex items-center justify-end gap-2">
          <div>
            <div className="body-md-medium-14">P2P-4242e</div>
          </div>
          <AppButton
            variant="secondary"
            className="flex w-[80px] items-center gap-1"
          >
            <EditIcon /> Edit
          </AppButton>
        </div>
      </div>

      {isShowModalEditProfile && (
        <ModalEditProfile
          isOpen={isShowModalEditProfile}
          onClose={() => setIsShowModalEditProfile(false)}
        />
      )}
    </div>
  );
};

export default SettingsProfile;

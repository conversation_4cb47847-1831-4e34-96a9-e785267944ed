"use client";

import React from "react";
import { AppButton } from "@/components";

const SettingsLinkAccount = () => {
  return (
    <div className="border-white-100 border-b p-4 lg:rounded-[16px] lg:border">
      <div className="heading-lg-medium-24 text-white-500 text-[18px] lg:text-[24px]">
        Link Account
      </div>

      <div className="mt-4 flex justify-between gap-4">
        <div className="flex-1">
          <div className="heading-sm-medium-16 mb-1">Withdral Whitelist</div>
          <div className="body-md-regular-14 text-white-500 max-w-[530px]">
            Link your X Account to VDAX
          </div>
        </div>

        <div className="flex items-center gap-2">
          <div>
            <div className="body-sm-medium-12">Not Linked</div>
          </div>
          <AppButton variant="secondary" className="w-[80px]">
            Link
          </AppButton>
        </div>
      </div>
    </div>
  );
};

export default SettingsLinkAccount;

"use client";

import React from "react";
import SettingsProfile from "./_parts/Profile";
import SettingsLinkAccount from "./_parts/LinkAccount";
import SettingsTrade from "./_parts/Trade";
import SettingsNotification from "./_parts/Notification";
import SettingsPrivacy from "./_parts/Privacy";
import SettingsWithdrawal from "./_parts/Withdrawal";
import SettingsReferences from "./_parts/References";

const MySettingsPage = () => {
  return (
    <div className="flex flex-col lg:gap-8">
      <SettingsProfile />
      <SettingsNotification />
      <SettingsReferences />
      <SettingsTrade />
      <SettingsWithdrawal />
      <SettingsLinkAccount />
      <SettingsPrivacy />
    </div>
  );
};

export default MySettingsPage;

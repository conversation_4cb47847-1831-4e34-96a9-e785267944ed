import React from "react";
import { ChevronDownIcon } from "@/assets/icons";

export default function AnnouncementNotificationPage() {
  return (
    <div className="border-white-100 rounded-[16px] border p-4 lg:h-[calc(100vh-100px)]">
      <div className="heading-sm-medium-16 lg:text-white-500">Announcement</div>

      <div className="border-white-100 flex items-center justify-between gap-4 border-b px-4 py-6">
        <div className="body-md-regular-14">
          VDAX Will Support the eCash (XEC) Network Upgrade & Hard Fork -
          2025-05-15
        </div>

        <div className="body-md-regular-14 text-white-500 flex items-center gap-4">
          05/08/2025 11:00:02 <ChevronDownIcon className="rotate-[-90deg]" />
        </div>
      </div>
    </div>
  );
}

import React from "react";
import { ChevronDownIcon } from "@/assets/icons";

export default function AccountAssistantNotificationPage() {
  return (
    <div className="border-white-100 rounded-[16px] border p-4 lg:h-[calc(100vh-100px)]">
      <div className="heading-sm-medium-16 lg:text-white-500">
        Account Assistant
      </div>

      <div className="border-white-100 flex items-center justify-between gap-4 border-b py-4">
        <div>
          <div className="body-md-semibold-14">
            Login with tempted from new IP
          </div>
          <div className="body-sm-regular-12 text-white-500">
            The system has detected that your account is logged in an unused IP
            address
          </div>
          <div className="body-md-medium-14 mt-2 cursor-pointer underline">
            View More
          </div>
        </div>

        <div className="body-md-regular-14 text-white-500 flex items-center gap-4">
          05/08/2025 11:00:02 <ChevronDownIcon className="rotate-[-90deg]" />
        </div>
      </div>
    </div>
  );
}

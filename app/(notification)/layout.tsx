"use client";

import React, { ReactNode } from "react";
import { Header, Sidebar } from "@/layouts";
import {
  AnnouncementIcon,
  GiftIcon,
  EmailIcon20,
  ProfileIcon,
} from "@/assets/icons";

const MENUS_NOTIFICATION = [
  {
    name: "Announcement",
    path: "/notification/announcement",
    icon: <AnnouncementIcon />,
  },
  {
    name: "Campaign",
    icon: <GiftIcon />,
    path: "/notification/campaign",
  },
  {
    name: "Marketing & Transaction",
    icon: <EmailIcon20 />,
    path: "/notification/marketing-transaction",
  },
  {
    name: "Account Assistant",
    icon: <ProfileIcon />,
    path: "/notification/account-assistant",
  },
];

export default function Layout({ children }: { children: ReactNode }) {
  return (
    <>
      <Header />
      <div className="bg-black-900 min-h-screen pt-[50px]">
        <div className="mx-auto flex w-full max-w-[1440px] flex-col lg:flex-row lg:gap-6 lg:px-10 lg:py-6">
          <Sidebar menus={MENUS_NOTIFICATION} />
          <div className="lg:flex-1">{children}</div>
        </div>
      </div>
    </>
  );
}

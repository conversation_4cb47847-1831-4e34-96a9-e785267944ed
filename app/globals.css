@import "tailwindcss";

@font-face {
  font-family: "Mona-Sans";
  src: local("Mona-Sans"), url("/fonts/MonaSans-Regular.ttf") format("truetype");
  font-weight: 400;
}

@font-face {
  font-family: "Mona-Sans";
  src: local("Mona-Sans"), url("/fonts/MonaSans-Medium.ttf") format("truetype");
  font-weight: 500;
}

@font-face {
  font-family: "Mona-Sans";
  src: local("Mona-Sans"),
    url("/fonts/MonaSans-SemiBold.ttf") format("truetype");
  font-weight: 600;
}

@font-face {
  font-family: "Mona-Sans";
  src: local("Mona-Sans"),
  url("/fonts/MonaSans-ExtraBold.ttf") format("truetype");
  font-weight: 800;
}

:root {
  --background: #08090c;
  --foreground: #ffffff;
}

@layer base {
  .body-xs-regular-8 {
    font-size: 8px;
    font-style: normal;
    font-weight: 400;
    line-height: 14px;
  }
  .body-xs-medium-8 {
    font-size: 8px;
    font-style: normal;
    font-weight: 500;
    line-height: 14px;
  }
  .body-xs-regular-9 {
    font-size: 9px;
    font-style: normal;
    font-weight: 400;
    line-height: 14px;
  }
  .body-xs-medium-9 {
    font-size: 9px;
    font-style: normal;
    font-weight: 500;
    line-height: 14px;
  }
  .body-xs-regular-10 {
    font-size: 10px;
    font-style: normal;
    font-weight: 400;
    line-height: 16px;
  }

  .body-xs-medium-10 {
    font-size: 10px;
    font-style: normal;
    font-weight: 500;
    line-height: 16px;
  }

  .body-sm-semibold-12 {
    font-size: 12px;
    font-style: normal;
    font-weight: 600;
    line-height: 18px;
  }

  .body-sm-regular-12 {
    font-size: 12px;
    font-style: normal;
    font-weight: 400 !important;
    line-height: 18px;
  }

  .body-sm-medium-12 {
    font-size: 12px;
    font-style: normal;
    font-weight: 500;
    line-height: 18px;
  }

  .action-sm-medium-14 {
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    line-height: 18px;
  }

  .action-sm-semibold-12 {
    font-size: 12px;
    font-style: normal;
    font-weight: 600;
    line-height: 16px;
  }

  .action-xs-medium-12 {
    font-size: 12px;
    font-style: normal;
    font-weight: 500;
    line-height: 16px;
  }

  .body-md-semibold-14 {
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: 20px;
  }

  .body-md-medium-14 {
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    line-height: 20px;
  }

  .body-md-regular-14 {
    font-size: 14px;
    font-style: normal;
    font-weight: 400 !important;
    line-height: 20px;
  }

  .heading-lg-semibold-24 {
    font-size: 24px;
    font-style: normal;
    font-weight: 600;
    line-height: 120%;
  }

  .heading-lg-semibold-32 {
    font-size: 32px;
    font-style: normal;
    font-weight: 600;
    line-height: 120%;
  }

  .heading-lg-medium-24 {
    font-size: 24px;
    font-style: normal;
    font-weight: 500;
    line-height: 120%;
  }

  .heading-md-medium-18 {
    font-size: 18px;
    font-style: normal;
    font-weight: 600;
    line-height: 120%;
  }

  .heading-md-medium-20 {
    font-size: 20px;
    font-style: normal;
    font-weight: 600;
    line-height: 120%;
  }

  .heading-sm-medium-16 {
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: 24px;
  }

  .heading-xlg-semibold-32 {
    font-size: 32px;
    font-style: normal;
    font-weight: 600;
    line-height: 120%;
  }

  .body-2xs-regular-8 {
    font-size: 8px;
    font-style: normal;
    font-weight: 400;
    line-height: 14px;
  }

  .heading-2xl-semibold-40 {
    font-size: 40px;
    font-style: normal;
    font-weight: 600;
    line-height: 120%;
  }

  .body-sm-regular-11 {
    font-size: 11px;
    font-style: normal;
    font-weight: 500;
    line-height: 18px;
  }

  .heading-sm-semibold-16 {
    font-size: 16px;
    font-style: normal;
    font-weight: 600;
    line-height: 24px;
  }

  .heading-md-semibold-18 {
    font-size: 18px;
    font-style: normal;
    font-weight: 600;
    line-height: 120%;
  }
}

@theme {
  --color-red-400: #ec5d59;
  --color-red-500: #e32225;
  --color-red-800: rgba(191, 4, 0, 0.3);
  --color-red-900: rgba(191, 4, 0, 0.1);

  --color-green-500: #2dbd85;
  --color-green-600: rgba(39, 217, 113, 0.9);
  --color-green-700: rgba(39, 217, 113, 0.6);
  --color-green-900: rgba(39, 217, 113, 0.1);

  --color-black-50: rgba(8, 9, 12, 0.05);
  --color-black-800: rgba(8, 9, 12, 0.80);
  --color-black-900: #08090c;

  --color-white-25: rgba(255, 255, 255, 0.02);
  --color-white-50: rgba(255, 255, 255, 0.05);
  --color-white-100: rgba(255, 255, 255, 0.1);
  --color-white-150: rgba(255, 255, 255, 0.15);
  --color-white-200: rgba(255, 255, 255, 0.2);
  --color-white-300: rgba(255, 255, 255, 0.3);
  --color-white-400: rgba(255, 255, 255, 0.4);
  --color-white-500: rgba(255, 255, 255, 0.5);
  --color-white-600: rgba(255, 255, 255, 0.6);
  --color-white-700: rgba(255, 255, 255, 0.7);
  --color-white-800: rgba(255, 255, 255, 0.8);
  --color-white-900: rgba(255, 255, 255, 0.9);
  --color-white-1000: #ffffff;

  --color-orange-500: #f68b1e;

  --color-brand-500: #009c4d;
  --color-brand-800: rgba(2, 156, 67, 0.3);
  --color-brand-900: rgba(2, 156, 67, 0.1);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

* {
  font-family: "Mona-Sans", sans-serif;
}

body {
  color: var(--foreground);
  background: var(--background);
  font-family: "Mona-Sans", sans-serif;
  font-size: 14px;
}

.hide-scroll::-webkit-scrollbar {
  display: none;
}

.customer-scroll::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 4px;
}

.customer-scroll::-webkit-scrollbar {
  width: 2px;
  background: transparent;
  cursor: pointer;
}

.customer-scroll::-webkit-scrollbar-thumb {
  border-radius: 4px;
  background-color: rgba(255, 255, 255, 0.1);
}

.rc-tooltip-inner {
  padding: 0;
  border-radius: 4px;
  border: 1px solid rgba(255, 255, 255, 0.05);
  background: #212224 !important;
  box-shadow: 0 0 4px 0 rgba(8, 9, 12, 0.5);
}

.rc-tooltip {
  opacity: 1 !important;
}

.rc-tooltip-placement-top,
.rc-tooltip-placement-left,
.rc-tooltip-placement-right,
.rc-tooltip-placement-bottom {
  padding: 5px !important;
}
body::-webkit-scrollbar {
  display: none;
}

.bg-gradient-text {
  background: linear-gradient(
    91deg,
    var(--Black-800, rgba(8, 9, 12, 0.8)) -12.5%,
    #fff 39.79%,
    #fff 63.03%,
    var(--Black-800, rgba(8, 9, 12, 0.8)) 103.7%
  );
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.bg-gradient-text-green {
  background: var(
    --Gradient-line,
    linear-gradient(
      90deg,
      rgba(91, 208, 141, 0) 0%,
      #5bd08d 20%,
      #5bd08d 50%,
      #5bd08d 80%,
      #5bd08d 80%
    )
  );
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

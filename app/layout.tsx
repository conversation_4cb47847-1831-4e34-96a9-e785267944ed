import type { Metadata } from "next";
import "./globals.css";
import { AppProvider } from "@/app/provider";
import "rc-tooltip/assets/bootstrap.css";
import "react-datepicker/dist/react-datepicker.css";
import { ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { cookies } from "next/headers";
import { COOKIES_ACCESS_TOKEN_KEY } from "@/constants";

export const metadata: Metadata = {
  title: "VDAX",
  description: "Get Verified & Start Your Crypto Journey",
};

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const accessToken =
    (await cookies()).get(COOKIES_ACCESS_TOKEN_KEY)?.value || "";

  return (
    <html lang="en">
      <body>
        <AppProvider authorization={accessToken}>
          <ToastContainer
            autoClose={2000}
            position="top-right"
            icon={false}
            pauseOnHover
            closeButton={false}
            hideProgressBar
            toastStyle={{
              position: "relative",
              overflow: "visible",
            }}
          />
          <div className="min-h-screen">{children}</div>
        </AppProvider>
      </body>
    </html>
  );
}

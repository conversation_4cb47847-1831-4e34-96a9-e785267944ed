"use client";

import { App<PERSON>utton } from "@/components";
import React, { useMemo, useState } from "react";
import { ChevronDownIcon, AddressBook } from "@/assets/icons";
import { NumericFormat } from "react-number-format";
import { SelectNetwork } from "../components/SelectNetwork";
import { SelectCoin } from "../components/SelectCoin";
import { useSelector } from "react-redux";
import { RootState } from "@/store/index";
import rf from "@/services/RequestFactory";
import { errorMsg, successMsg } from "@/libs/toast";
import useAccountBalance from "@/hooks/useAccountBalance";
import { TAsset } from "@/types/asset";
import { TBalance } from "@/types/account";
import { AppNumber } from "@/components/AppNumber";
import BigNumber from "bignumber.js";
import {
  ModalConfirmWithdraw,
  ModalWithdrawVerification,
  ModalWithdrawSuccess,
} from "@/modals";
import { TNetwork } from "@/types/network";
import { filterParams, isValidEmail } from "@/utils/helper";

export enum EType {
  ADDRESS = "ADDRESS",
  VDAX_USER = "VDAX_USER",
}

export enum ETypeUser {
  EMAIL = "EMAIL",
  VDAX_ID = "VDAX_ID",
}

const TABS = [
  {
    name: "Address",
    value: EType.ADDRESS,
  },
  {
    name: "VDAX User",
    value: EType.VDAX_USER,
  },
];

export const WithdrawForm = () => {
  const [token, setToken] = useState<string>("");
  const [address, setAddress] = useState<string>("");
  const [email, setEmail] = useState<string>("");
  const [userId, setUserId] = useState<string>("");
  const [networkId, setNetworkId] = useState<string | number>("");
  const [amount, setAmount] = useState<any>("");
  const [type, setType] = useState<string>(EType.ADDRESS);
  const [typeUser, setTypeUser] = useState<ETypeUser>(ETypeUser.EMAIL);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [note, setNote] = useState<string>("");

  const [isShowModalWithdrawConfirm, setIsShowModalWithdrawConfirm] =
    useState<boolean>(false);
  const [isShowModalWithdrawVerify, setIsShowModalWithdrawVerify] =
    useState<boolean>(false);
  const [isShowModalWithdrawSuccess, setIsShowModalWithdrawSuccess] =
    useState<boolean>(false);

  const networks = useSelector((state: RootState) => state.metadata.networks);
  const assets = useSelector((state: RootState) => state.metadata.assets);
  const { accountBalances } = useAccountBalance({});

  const coinsOptions = useMemo(
    () =>
      assets.map((a: TAsset) => {
        const coinAvailable = accountBalances.find(
          (c: TBalance) => c.asset?.toLowerCase() === a.symbol?.toLowerCase()
        );
        return {
          ...a,
          available: coinAvailable?.available || "0",
        } as TAsset;
      }),
    [accountBalances, assets]
  );

  const isCompleteStep1 = !!token;
  const isCompleteStep2 = useMemo(() => {
    if (type === EType.ADDRESS) {
      return !!address && networkId;
    }

    if (typeUser === ETypeUser.EMAIL) {
      return !!email && isValidEmail(email);
    }

    return !!userId;
  }, [email, userId, typeUser, type, networkId, address]);

  const coinSelected = useMemo(
    () =>
      coinsOptions.find(
        (item) => item.symbol?.toLowerCase() === token?.toLowerCase()
      ) as TAsset,
    [coinsOptions, token]
  );

  const networkSelected = useMemo(
    () => networks.find((item) => item.id === networkId) as TNetwork,
    [networks, networkId]
  );

  const onWithdraw = async () => {
    try {
      setIsLoading(true);
      await rf.getRequest("AccountRequest").withdraw({
        asset: token,
        networkId: +networkId,
        amount: +amount,
        toAddress: address || null,
        userId: userId || null,
      });
      setIsShowModalWithdrawVerify(true);
      setIsShowModalWithdrawConfirm(false);
      setIsLoading(false);
    } catch (e) {
      errorMsg("Withdraw Failed");
      setIsLoading(false);
      console.error(e, "Withdraw Error");
    }
  };

  return (
    <div className="w-full lg:pl-8">
      <div className="flex gap-3 ">
        <div className="flex flex-col items-center">
          <div className="bg-brand-500 h-4 w-4 rotate-[45deg] text-center">
            <div className="body-sm-medium-12 rotate-[-45deg] pl-0.5 text-center">
              {isCompleteStep1 ? "✓" : "1"}
            </div>
          </div>
          <div
            className={`${
              isCompleteStep1 ? "bg-brand-500" : "bg-white-500"
            } w-[1px] flex-1`}
          />
        </div>

        <div className="w-full pb-5">
          <div className="body-md-semibold-14 mb-2.5">Select Coin</div>
          <SelectCoin token={token} setToken={setToken} tokens={coinsOptions} />
        </div>
      </div>

      <div className="flex gap-3">
        <div className="flex flex-col items-center">
          <div
            className={`${
              isCompleteStep1 ? "bg-brand-500" : "bg-white-500"
            } h-4 w-4 rotate-[45deg] text-center`}
          >
            <div className="body-sm-medium-12 rotate-[-45deg] pl-0.5 text-center">
              {isCompleteStep2 ? "✓" : "2"}
            </div>
          </div>
          <div
            className={`${
              isCompleteStep2 ? "bg-brand-500" : "bg-white-500"
            } w-[1px] flex-1`}
          />
        </div>

        <div className="w-full pb-8">
          <div
            className={`${
              isCompleteStep1 ? "" : "text-white-500"
            } body-md-semibold-14 mb-2.5`}
          >
            Withdraw to
          </div>
          {isCompleteStep1 && (
            <>
              <div className="border-white-50 mb-4 flex w-max border-b">
                {TABS.map((item, index) => {
                  return (
                    <div
                      onClick={() => setType(item.value)}
                      className={`-mb-[1px] cursor-pointer px-1 py-1 lg:px-4 lg:py-2.5 ${
                        item.value === type
                          ? "text-white-1000 body-sm-semibold-12 border-white-500 border-b"
                          : "text-white-500 body-sm-medium-12"
                      }`}
                      key={index}
                    >
                      {item.name}
                    </div>
                  );
                })}
              </div>

              {type === EType.VDAX_USER && (
                <div className="mb-4 flex gap-2">
                  <div
                    onClick={() => setTypeUser(ETypeUser.EMAIL)}
                    className={`body-xs-medium-10 cursor-pointer rounded-[4px] px-2 py-1 ${
                      typeUser === ETypeUser.EMAIL ? "bg-white-100" : ""
                    }`}
                  >
                    Email
                  </div>
                  <div
                    onClick={() => setTypeUser(ETypeUser.VDAX_ID)}
                    className={`body-xs-medium-10 cursor-pointer rounded-[4px] px-2 py-1 ${
                      typeUser === ETypeUser.VDAX_ID ? "bg-white-100" : ""
                    }`}
                  >
                    VDAX ID
                  </div>
                </div>
              )}

              {type === EType.ADDRESS ? (
                <div className="border-white-100 flex w-full cursor-pointer items-center justify-between gap-2 rounded-[6px] border p-2">
                  <input
                    className="placeholder:text-white-300 body-sm-regular-12 flex-1 truncate bg-transparent outline-none"
                    value={address}
                    onChange={(e) => setAddress(e.target.value)}
                    placeholder="Enter Address"
                  />
                  <AddressBook />
                </div>
              ) : (
                <>
                  {typeUser === ETypeUser.EMAIL ? (
                    <div className="border-white-100 flex w-full cursor-pointer items-center justify-between gap-2 rounded-[6px] border p-2">
                      <input
                        className="placeholder:text-white-300 body-sm-regular-12 flex-1 truncate bg-transparent outline-none"
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                        placeholder="Enter Email Address"
                      />
                    </div>
                  ) : (
                    <div className="border-white-100 flex w-full cursor-pointer items-center justify-between gap-2 rounded-[6px] border p-2">
                      <input
                        className="placeholder:text-white-300 body-sm-regular-12 flex-1 truncate bg-transparent outline-none"
                        value={userId}
                        onChange={(e) => setUserId(e.target.value)}
                        placeholder="Enter VDAX ID"
                      />
                    </div>
                  )}
                </>
              )}

              {type === EType.ADDRESS && (
                <div className="mt-4">
                  <SelectNetwork
                    networkId={networkId}
                    setNetworkId={setNetworkId}
                    networks={networks}
                  />
                </div>
              )}
            </>
          )}
        </div>
      </div>

      <div className="flex gap-3">
        <div className="flex flex-col items-center">
          <div
            className={`${
              isCompleteStep2 ? "bg-brand-500" : "bg-white-500"
            } h-4 w-4 rotate-[45deg] text-center`}
          >
            <div className="body-sm-medium-12 rotate-[-45deg] pl-0.5 text-center">
              3
            </div>
          </div>
        </div>

        <div className=" w-full">
          <div
            className={`${
              isCompleteStep2 ? "" : "text-white-500"
            } body-md-semibold-14 mb-2.5`}
          >
            Withdraw Amount
          </div>
          {isCompleteStep2 && (
            <>
              <div className="border-white-100 flex w-full cursor-pointer items-center justify-between gap-2 rounded-[6px] border p-2">
                <NumericFormat
                  value={amount}
                  placeholder={"0.00"}
                  onValueChange={(values) => setAmount(values.value)}
                  thousandSeparator=","
                  decimalSeparator="."
                  allowNegative={false}
                  className="body-sm-medium-12 placeholder:text-white-500 ml-auto flex-1 bg-transparent focus:outline-none"
                  allowLeadingZeros={false}
                  decimalScale={6}
                  inputMode="numeric"
                />
                <div className="body-md-regular-14">{token}</div>
                <div
                  className="text-brand-500 body-md-regular-14"
                  onClick={() => setAmount(coinSelected?.available || "0")}
                >
                  MAX
                </div>
              </div>

              {type === EType.VDAX_USER && (
                <div className="border-white-100 mt-4 flex w-full cursor-pointer items-center justify-between gap-2 rounded-[6px] border p-2">
                  <input
                    className="placeholder:text-white-300 body-sm-regular-12 flex-1 truncate bg-transparent outline-none"
                    value={note}
                    onChange={(e) => setNote(e.target.value)}
                    placeholder="Enter note"
                  />
                </div>
              )}

              <div className="mt-2 flex items-center justify-between">
                <div className="text-white-500 body-md-regular-14">
                  Available Withdraw
                </div>
                <div className="body-sm-medium-12 flex items-center gap-2">
                  <AppNumber value={coinSelected?.available || "0"} /> {token}{" "}
                  <ChevronDownIcon />
                </div>
              </div>
              <div className="mt-2 flex items-center justify-between">
                <div className="text-white-500 body-md-regular-14">
                  24h remaining limit
                </div>
                <div className="body-sm-medium-12">
                  7,999,384.6 /{" "}
                  <span className="text-white-500">8,000,000.0 {token}</span>
                </div>
              </div>

              <div className="border-white-100 mt-4 flex items-center justify-between border-t pt-4">
                <div>
                  <div className="text-white-500 body-md-regular-14">
                    Receive amount
                  </div>

                  <div className="heading-sm-medium-16 mb-1 flex items-center gap-1">
                    <AppNumber value={amount} /> {token}
                  </div>
                  <div className="text-white-500 body-md-regular-14 flex items-center gap-2">
                    Network fee 0.005 USDT
                    <ChevronDownIcon className="rotate-[-90deg]" />
                  </div>
                </div>

                <AppButton
                  disabled={
                    isLoading ||
                    !+amount ||
                    new BigNumber(amount).gt(coinSelected?.available || "0")
                  }
                  variant="buy"
                  onClick={() => setIsShowModalWithdrawVerify(true)}
                >
                  {isLoading ? "Loading..." : "Withdraw"}
                </AppButton>
              </div>
            </>
          )}
        </div>
      </div>

      {isShowModalWithdrawVerify && (
        <ModalWithdrawVerification
          isOpen={isShowModalWithdrawVerify}
          onClose={() => setIsShowModalWithdrawVerify(false)}
        />
      )}

      {isShowModalWithdrawSuccess && (
        <ModalWithdrawSuccess
          isOpen={isShowModalWithdrawSuccess}
          onClose={() => setIsShowModalWithdrawSuccess(false)}
        />
      )}

      {isShowModalWithdrawConfirm && (
        <ModalConfirmWithdraw
          isLoading={isLoading}
          email={email}
          note={note}
          userId={userId}
          typeUser={typeUser}
          type={type}
          address={address}
          token={token}
          amount={amount}
          network={networkSelected}
          isOpen={isShowModalWithdrawConfirm}
          onClose={() => setIsShowModalWithdrawConfirm(false)}
          onWithdraw={onWithdraw}
        />
      )}
    </div>
  );
};
